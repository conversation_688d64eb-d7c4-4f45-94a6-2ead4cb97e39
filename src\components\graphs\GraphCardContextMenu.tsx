import { PencilIcon } from "@heroicons/react/24/outline";
import { TrashIcon } from "@heroicons/react/24/outline";
import { DocumentDuplicateIcon } from "@heroicons/react/24/outline";
import MenuButton from "@/components/ui/MenuButton";

export default function GraphCardContextMenu({
  startRename,
  handleDuplicate,
  handleDelete,
}: {
  startRename: (e: React.MouseEvent) => void;
  handleDuplicate: (e: React.MouseEvent) => void;
  handleDelete: (e: React.MouseEvent) => void;
}) {
  return (
    <div className="absolute right-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10">
      <div className="py-1">
        <MenuButton 
          onClick={startRename}
          icon={<PencilIcon className="h-4 w-4 mr-2" />}
          label="Rename"
        />
        <MenuButton 
          onClick={handleDuplicate}
          icon={<DocumentDuplicateIcon className="h-4 w-4 mr-2" />}
          label="Duplicate"
        />
        <MenuButton 
          onClick={handleDelete}
          icon={<TrashIcon className="h-4 w-4 mr-2" />}
          label="Delete"
          variant="danger"
        />
      </div>
    </div>
  );
}

