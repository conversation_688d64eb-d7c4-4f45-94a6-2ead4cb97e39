import { useCallback, useMemo } from "react";
import { Edge, Node } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useNodeAncestors } from "./useNodeAncestors";
import {
  EDGE,
  NODE,
} from "@/lib/constants";
import { EdgeBuilder } from "@/utils/edgeBuilder";
import { EdgeTagFilter } from "@/types/tagFilter";
import { NodeTag } from "@/utils/nodeBuilder";

/**
 * Хук для подсветки пути от корня к текущему узлу
 */
export const useHighlightAncestorPath = () => {
  const { setEdges, setNodes } = useGraph();
  const getAncestors = useNodeAncestors();

  /**
   * Checks if node passes tag filter
   * @param nodeId - Node ID to check
   * @param tagFilter - Array of tags for filtering
   * @param nodes - Array of nodes
   * @returns True if node passes filter
   */
  const nodePassesFilter = useCallback((nodeId: string, tagFilter: EdgeTagFilter, nodes: Node[]): boolean => {
    // If filter is empty, all nodes pass
    if (!tagFilter || tagFilter.length === 0) {
      return true;
    }
    
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return false;
    
    const nodeTags = node.data?.tags as NodeTag[] || [];
    
    // Node passes if it has at least one of the tags in filter (OR logic)
    return nodeTags.some(tag => tagFilter.includes(tag.id));
  }, []);

  /**
   * Highlights path from root to specified node
   * @param nodeId ID of node to highlight path for
   */
  const highlightPath = useCallback(
    (nodeId: string | null, nodes: Node[], edges: Edge[]) => {
      if (!nodeId) {
        // If node is not specified, reset all styles but keep selected lines
        setEdges((eds) =>
          eds.map((edge) => {
            // Check if line is blocked
            const isBlocked = edge.data?.isBlocked === true;
            
            // Get tag filter for this edge
            const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
            
            // Check if target node passes filter
            const hasTagFilter = tagFilter.length > 0;
            const targetNodeId = edge.target;
            const targetNodePasses = nodePassesFilter(targetNodeId, tagFilter, nodes);
            
            // Edge is filtered if it has filter and target node doesn't pass
            const isFiltered = hasTagFilter && !targetNodePasses;
            
            // Use helper method to get styles
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath: false,
              isSelected: edge.selected || false,
              isFiltered
            });
            
            return {
              ...edge,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              markerEnd: edgeStyles.markerEnd,
            };
          })
        );

        // Reset node highlighting
        setNodes((nds) =>
          nds.map((node) =>
            node.selected || node.data?.isInPath
              ? node
              : {
                  ...node,
                  data: {
                    ...node.data,
                    isInPath: false,
                  },
                  style: {
                    ...node.style,
                    border: NODE.BORDER.DEFAULT,
                    background: NODE.BACKGROUNDS.DEFAULT,
                  },
                }
          )
        );
        return;
      }

      // Get ancestor chain
      const ancestors = getAncestors(nodeId, nodes, edges);

      // Create set of node IDs in chain (including current node)
      const ancestorIds = new Set([nodeId, ...ancestors.map((a) => a.id)]);

      // Create set of edges included in path
      const pathEdgeIds = new Set();

      // Determine which edges are in the path
      edges.forEach((edge) => {
        if (ancestorIds.has(edge.source) && ancestorIds.has(edge.target)) {
          pathEdgeIds.add(edge.id);
        }
      });

      // Update edge styles
      setEdges((eds) =>
        eds.map((edge) => {
          // Check if edge is in path
          const isInPath = pathEdgeIds.has(edge.id);
          
          // Get tag filter for this edge
          const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
          
          // Check if target node passes filter
          const hasTagFilter = tagFilter.length > 0;
          const targetNodeId = edge.target;
          const targetNodePasses = nodePassesFilter(targetNodeId, tagFilter, nodes);
          
          // Edge is filtered if it has filter and target node doesn't pass
          const isFiltered = hasTagFilter && !targetNodePasses;
          
          if (edge.selected) {
            // Check if line is blocked
            const isBlocked = edge.data?.isBlocked === true;
            
            // Use helper method to get styles
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath,
              isSelected: true,
              isFiltered
            });
            
            return {
              ...edge,
              data: {
                ...edge.data,
                isInPath,
              },
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              markerEnd: edgeStyles.markerEnd
            };
          }

          // For non-selected lines
          if (isInPath) {
            // Check if line is blocked
            const isBlocked = edge.data?.isBlocked === true;
            
            // Use helper method to get styles
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath: true,
              isSelected: false,
              isFiltered
            });
            
            return {
              ...edge,
              markerEnd: edgeStyles.markerEnd,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              data: {
                ...edge.data,
                isInPath: true,
                // Preserve blocking state
                isBlocked: edge.data?.isBlocked,
              },
            };
          } else {
            // Check if line is blocked
            const isBlocked = edge.data?.isBlocked === true;
            
            // Use helper method to get styles
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath: false,
              isSelected: edge.selected || false,
            });
            
            return {
              ...edge,
              markerEnd: edgeStyles.markerEnd,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              data: {
                ...edge.data,
                isInPath: false,
              },
            };
          }
        })
      );

      // Update node styles
      setNodes((nds) =>
        nds.map((node) => {
          // Check if node is in current path
          const isInCurrentPath = ancestorIds.has(node.id);
          
          // If node is not in path, reset its style to default
          if (!isInCurrentPath) {
            return {
              ...node,
              style: {
                ...node.style,
                border: NODE.BORDER.DEFAULT,
                background: NODE.BACKGROUNDS.DEFAULT,
              },
              data: {
                ...node.data,
                isInPath: false,
              },
            };
          }
          
          // If node is in path, apply highlight style
          return {
            ...node,
            style: {
              ...node.style,
              border: NODE.BORDER.HIGHLIGHTED,
            },
            data: {
              ...node.data,
              isInPath: true,
            },
          };
        })
      );
    },
    [setEdges, setNodes, getAncestors, nodePassesFilter]
  );

  /**
   * Resets highlight for all paths
   */
  const resetHighlight = useCallback(
    (nodes: Node[], edges: Edge[]) => {
      highlightPath(null, nodes, edges);
    },
    [highlightPath]
  );

  return useMemo(
    () => ({
      highlightPath,
      resetHighlight,
      EDGE,
    }),
    [highlightPath, resetHighlight]
  );
};

export default useHighlightAncestorPath;
