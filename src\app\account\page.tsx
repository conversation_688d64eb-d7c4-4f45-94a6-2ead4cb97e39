import { UserProfile } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { Metadata } from "next";
import { ROUTES } from "@/lib/constants";

export const metadata: Metadata = {
  title: "Account Settings | GraphChat",
  description: "Manage your account settings and preferences",
};

export default async function AccountPage(): Promise<JSX.Element> {
  const { userId } = await auth();

  if (!userId) {
    return redirect(ROUTES.LOGIN);
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-8 text-gray-800 dark:text-gray-100">
        Account Settings
      </h1>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <UserProfile />
      </div>
    </div>
  );
}
