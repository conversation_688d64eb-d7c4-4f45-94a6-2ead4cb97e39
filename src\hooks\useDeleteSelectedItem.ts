import { useCallback, useMemo } from "react";
import { useGraph } from "@/contexts/GraphContext";
import { useDeleteNode } from "./useDeleteNode";
import { useDeleteConnection } from "./useDeleteConnection";

/**
 * Custom hook to delete either a selected node or a selected edge
 * Determines what to delete based on current selection state
 */
export const useDeleteSelectedItem = () => {
  const { selectedNodeId, edges } = useGraph();
  const deleteNode = useDeleteNode();
  const { deleteConnection } = useDeleteConnection();

  /**
   * Deletes the currently selected item (node or edge)
   * If a node is selected, it deletes the node
   * If an edge is selected, it deletes the edge
   */
  const deleteSelectedItem = useCallback(() => {
    // Check if there's a selected node
    if (selectedNodeId) {
      deleteNode(selectedNodeId);
      return;
    }

    // Check if there's a selected edge
    const selectedEdge = edges.find(edge => edge.selected);
    if (selectedEdge) {
      deleteConnection(selectedEdge.id);
    }
  }, [selectedNodeId, edges, deleteNode, deleteConnection]);

  /**
   * Checks if there is any item (node or edge) selected
   * @returns boolean indicating if any item is selected
   */
  const hasSelectedItem = useMemo(() => {
    // Check if there's a selected node
    if (selectedNodeId) {
      return true;
    }

    // Check if there's a selected edge
    return edges.some(edge => edge.selected);
  }, [selectedNodeId, edges]);

  return {
    deleteSelectedItem,
    hasSelectedItem
  };
};

export default useDeleteSelectedItem;
