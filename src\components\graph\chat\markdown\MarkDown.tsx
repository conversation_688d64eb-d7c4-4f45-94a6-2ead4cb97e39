// noinspection JSUnusedGlobalSymbols

// src/components/chat/markdown/MarkDown.tsx
import React, { memo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import CodeBlock from "./CodeBlock";

// Import KaTeX CSS
import 'katex/dist/katex.min.css';

interface MarkdownProps {
  children: string;
}

// Keep custom components for non-math elements as needed
const components = {
  // Lists
  ol: ({ children }: React.PropsWithChildren) => ( <ol className="list-decimal list-outside ml-5 my-2">{children}</ol> ),
  ul: ({ children }: React.PropsWithChildren) => ( <ul className="list-disc list-outside ml-5 my-2">{children}</ul> ),
  li: ({ children }: React.PropsWithChildren) => ( <li className="py-1">{children}</li> ),
  // Text formatting
  strong: ({ children }: React.PropsWithChildren) => ( <strong className="font-bold">{children}</strong> ),
  em: ({ children }: React.PropsWithChildren) => ( <em className="italic">{children}</em> ),
  // Code blocks
  code: ({ inline, className, children, ...props }: any) => {
    const match = /language-(\w+)/.exec(className || "");
    
    // Check if this code block is potentially math identified by rehype/remark
    if (className?.includes('language-math')) {
        // Let rehype-katex handle math blocks
        return inline ? (
          <code className="bg-gray-800 text-white px-1 py-0.5 rounded text-sm" {...props}>
            {children}
          </code>
        ) : null;
    }

    // Render standard code blocks
    return !inline && match ?  (
      <CodeBlock language={match[1]} value={String(children).replace(/\n$/, '')} />
    ) : (
      <code className="bg-gray-800 text-white px-1 py-0.5 rounded text-sm" {...props}>
        {children}
      </code>
    );
  },
  // Rest of your components remain the same...
  h1: ({ children }: React.PropsWithChildren) => ( <h1 className="text-2xl font-bold mt-6 mb-4">{children}</h1> ),
  h2: ({ children }: React.PropsWithChildren) => ( <h2 className="text-xl font-bold mt-5 mb-3">{children}</h2> ),
  h3: ({ children }: React.PropsWithChildren) => ( <h3 className="text-lg font-bold mt-4 mb-2">{children}</h3> ),
  p: ({ children }: React.PropsWithChildren) => ( <p className="my-3">{children}</p> ),
  a: ({ href, children }: React.PropsWithChildren<{ href?: string }>) => ( <a href={href} className="text-yellow-500  hover:underline cursor-pointer" target="_blank" rel="noopener noreferrer">{children}</a> ),
  table: ({ children }: React.PropsWithChildren) => ( <div className="overflow-x-auto my-4"><table className="min-w-full border border-gray-700 rounded">{children}</table></div> ),
  thead: ({ children }: React.PropsWithChildren) => ( <thead className="bg-gray-800">{children}</thead> ),
  tbody: ({ children }: React.PropsWithChildren) => ( <tbody className="divide-y divide-gray-700">{children}</tbody> ),
  tr: ({ children }: React.PropsWithChildren) => ( <tr className="border-b border-gray-700">{children}</tr> ),
  th: ({ children }: React.PropsWithChildren) => ( <th className="px-4 py-2 text-left font-medium text-white">{children}</th> ),
  td: ({ children }: React.PropsWithChildren) => ( <td className="px-4 py-2 text-gray-300">{children}</td> ),
  blockquote: ({ children }: React.PropsWithChildren) => ( <blockquote className="border-l-4 border-gray-500 pl-4 py-2 my-4 italic bg-gray-800 rounded-r">{children}</blockquote> ),
  hr: () => <hr className="my-6 border-gray-700" />,
};

// Base component without memoization for clarity
function BaseMarkdownComponent({ children }: MarkdownProps): JSX.Element {
  // Pass the raw markdown string directly to ReactMarkdown
  const unescapedChildren = children.replace(/\\([[\]()])/g, "$$");
  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath, remarkGfm]} // remark-math parses syntax
      rehypePlugins={[rehypeKatex]} // rehype-katex renders math nodes
      components={components} // Use custom components for non-math elements
    >
      {unescapedChildren}
    </ReactMarkdown>
  );
}

// Export the memoized version
export const Markdown = memo(
  BaseMarkdownComponent,
  (prevProps, nextProps) => prevProps.children === nextProps.children
);