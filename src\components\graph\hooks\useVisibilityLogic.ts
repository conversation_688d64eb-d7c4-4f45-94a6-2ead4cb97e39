import { useEffect } from "react";
import { useGraph } from "@/contexts/GraphContext";
import useVisibleNodes from "@/hooks/useVisibleNodes";

export default function useVisibilityLogic() {
  const ensureNodesVisible = useVisibleNodes();
  const { nodes } = useGraph();
  // Ensure all nodes are visible after any changes to nodes
  useEffect(() => {
    if (nodes.length > 0) {
      // Slight delay to allow for rendering
      const timer = setTimeout(() => {
        ensureNodesVisible();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [nodes, ensureNodesVisible]);
}
