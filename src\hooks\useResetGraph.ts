import { useCallback } from "react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

/**
 * Custom hook to handle resetting the graph to its initial state
 * @returns Function to reset the graph
 */
export const useResetGraph = () => {
  const {
    nodes,
    edges,
    messages,
    currentNodeId,
    setNodes,
    setEdges,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    setMessages,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();

  /**
   * Resets the graph to initial state
   */
  return useCallback(() => {
    // Save current state to history before resetting
    saveToHistory({
      nodes: [...nodes],
      edges: [...edges],
      selectedNodeId,
      bgColor,
      messages,
      currentNodeId,
    });

    // Reset nodes and edges to initial state
    setNodes([]);
    setEdges([]);
    setMessages([]);

    // Reset selected node and current node
    setSelectedNodeId(null);
    setCurrentNodeId(null);
  }, [
    nodes,
    edges,
    setNodes,
    setEdges,
    saveToHistory,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    setMessages,
    currentNodeId,
    messages,
  ]);
};
