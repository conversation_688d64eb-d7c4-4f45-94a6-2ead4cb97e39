import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

const isPublicRoute = createRouteMatcher([
    '/',                      // Homepage
    '/about',                 // About page
    '/terms',                 // Terms page
    '/privacy',               // Privacy page
    '/login(.*)',             // Login pages
    '/signup(.*)',            // Signup pages
    '/api/webhooks' // Webhook endpoint
	]);

export default clerkMiddleware(async (auth, request) => {
  if (!isPublicRoute(request)) { await auth.protect(); }
});

// Route configuration for middleware
export const config = {
  matcher: [
     '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
  ],
};
