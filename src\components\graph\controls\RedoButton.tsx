"use client";

import { IoMdRedo } from "react-icons/io";
import { ControlButton } from "./ControlButton";
import React from "react";
import { useRedoButtonLogic } from "./hooks/useRedoButtonLogic";

export const RedoButton = () => {
  const { redo, canRedo, title } = useRedoButtonLogic();
  return (
    <ControlButton
      icon={<IoMdRedo className="h-4 w-4" />}
      onClick={redo}
      disabled={!canRedo}
      title={title}
    />
  );
};