/* eslint-disable no-console */

import {NextRequest} from "next/server";
import {type IExtendedChatMessage} from "@/lib/types";
import {
    CHAT,
    ERRORS,
    Providers,
    PROVIDERS_MAP
} from "@/lib/constants";
import {generateStreamingResponse} from "@/lib/ai-config";
import {type Message as AIMessage} from "ai";
import {nanoid} from "nanoid";
import {aiProviders} from "@/lib/ai-provider";

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export type AIProviderKey = keyof typeof aiProviders;

// Define the expected request body type
interface ChatRequestBody {
    messages: Array<IExtendedChatMessage>;
    graphId?: string;
    provider?: AIProviderKey;
    model?: string;
}

/**
 * Chat API endpoint
 * Processes chat messages and returns streaming responses
 */
export async function POST(req: NextRequest) {
    
    try {
        // Parse the request body
        const {
            messages,
            graphId,
            provider = Providers.OPENAI as AIProviderKey,
            model
        }: ChatRequestBody = await req.json();

        // Get the model from the aiProviders object if it wasn't provided
        const defaultModel = PROVIDERS_MAP[provider]!.DEFAULT;

        const selectedModel = model || defaultModel;

        // Convert messages to the format expected by the AI SDK
        const aiMessages: AIMessage[] = messages.map((message) => ({
            id: nanoid(),
            role: message.role as "system" | "user" | "assistant",
            content: message.content,
            createdAt: new Date(),
        }));

        // Add graph context if graphId is provided
        if (graphId) {
            aiMessages.unshift({
                id: nanoid(),
                role: CHAT.ROLES.SYSTEM,
                content: `This conversation is about graph with ID: ${graphId}`,
                createdAt: new Date(),
            });
        }

        // Use the wrapper function to generate a streaming response
        const result = generateStreamingResponse(
            aiMessages,
            {aiSdk: aiProviders[provider].aiSdk, model: selectedModel},
            () => console.log("[API] Local callback: Stream completed successfully"),
        );

        // Return the stream with the correct response format
        return result.toDataStreamResponse({sendSources: true, getErrorMessage: (error: unknown) => {
                console.error('Server-side error:', error);
                return (error as Error).message;
            }});
    } catch (error) {
        console.error("[API] Error in chat API route:", error);

        const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
        console.error(`[API] Error message: ${errorMessage}`);

        return new Response(
            JSON.stringify({
                error: ERRORS.CHAT_GENERATION_FAILED,
                message: errorMessage,
            }),
            {
                status: 500,
                headers: {"Content-Type": "application/json"},
            },
        );
    }
}
