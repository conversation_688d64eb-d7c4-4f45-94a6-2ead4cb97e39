/* eslint-disable react-hooks/exhaustive-deps */
import { Edge } from "@xyflow/react";
import { useCallback, useMemo } from "react";

/**
 * Хук для работы с отношениями между узлами (родители и дети)
 */
export const useNodeRelations = () => {
  /**
   * Получает родительские узлы для указанного узла
   * @param nodeId ID узла, для которого нужно найти родителей
   * @returns Массив ID родительских узлов
   */
  const getParentNodes = useCallback(
    (nodeId: string, edges: Edge[]): string[] => {
      if (!edges || !nodeId) return [];

      // Родители - это узлы, из которых идут рёбра к текущему узлу
      return edges
        .filter((edge: Edge) => edge.target === nodeId)
        .map((edge: Edge) => edge.source);
    },
    [],
  );

  /**
   * Получает дочерние узлы для указанного узла
   * @param nodeId ID узла, для которого нужно найти детей
   * @returns Массив ID дочерних узлов
   */
  const getChildNodes = useCallback(
    (nodeId: string, edges: Edge[]): string[] => {
      if (!edges || !nodeId) return [];

      // Дети - это узлы, к которым идут рёбра от текущего узла
      return edges
        .filter((edge: Edge) => edge.source === nodeId)
        .map((edge: Edge) => edge.target);
    },
    [],
  );

  return useMemo(
    () => ({
      getParentNodes,
      getChildNodes,
    }),
    [],
  );
};

export default useNodeRelations;
