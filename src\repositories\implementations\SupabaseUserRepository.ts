/* eslint-disable no-console */
// noinspection ExceptionCaughtLocallyJS

import { getSupabaseClient, getSupabaseAdmin } from "@/lib/supabase-client";
import { IUserRepository } from "@/repositories";
import { UserDTO as User, CreateUserDTO, UpdateUserDTO } from "@/types/dto";

/**
 * Supabase implementation of the User repository
 */
export class SupabaseUserRepository implements IUserRepository {
  private supabasePromise;
  private sessionId?: string;
  private fallbackToAdmin: boolean = false;
  private isServerSide: boolean;

  constructor(sessionId?: string) {
    this.sessionId = sessionId;
    // Determine if code is running on the server
    this.isServerSide = typeof window === 'undefined';
    // If code is running on the server, use admin access immediately
    if (this.isServerSide) {
      this.fallbackToAdmin = true;
    }
    this.supabasePromise = this.getClient();
  }

  /**
   * Get Supabase client with error handling
   */
  private getClient() {
    if (this.fallbackToAdmin) {
      return Promise.resolve(getSupabaseAdmin());
    }

    try {
      return getSupabaseClient(this.sessionId);
    } catch (error) {
      console.error(`[SupabaseUserRepository] Error getting client, switching to admin access:`, error);
      this.fallbackToAdmin = true;
      return Promise.resolve(getSupabaseAdmin());
    }
  }

  /**
   * Get client with possible reload in case of error
   */
  private async getSafeClient() {
    try {
      return await this.supabasePromise;
    } catch (error) {
      console.error(`[SupabaseUserRepository] Error getting client, trying to reload`, error);
      this.supabasePromise = this.getClient();
      return this.supabasePromise;
    }
  }

  /**
   * Find a user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const supabase = await this.getSafeClient();
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error("[SupabaseUserRepository] Error finding user by ID:", error);
        
        // If error is related to token, switch to admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token'))) {
          this.fallbackToAdmin = true;
          this.supabasePromise = this.getClient();
          return this.findById(id);
        }
        
        return null;
      }
      
      if (!data) {
        return null;
      }
      
      return this.mapToUser(data);
    } catch (error) {
      console.error("[SupabaseUserRepository] Unexpected error in findById:", error);
      
      // For any unexpected error, try using admin access
      if (!this.fallbackToAdmin) {
        this.fallbackToAdmin = true;
        this.supabasePromise = this.getClient();
        return this.findById(id);
      }
      
      return null;
    }
  }

  /**
   * Find a user by Clerk ID
   */
  async findByClerkId(clerkId: string): Promise<User | null> {
    
    try {
      const supabase = await this.getSafeClient();
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('clerk_id', clerkId)
        .single();

      if (error) {
        // If error is related to user not found, this is expected behavior
        if (error.code === 'PGRST116') {
          console.error(`[SupabaseUserRepository] User with clerk_id ${clerkId} not found`);
          return null;
        }
        
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseUserRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = Promise.resolve(getSupabaseAdmin());
            return this.findByClerkId(clerkId);
          }
        }
        
        // For other types of errors, log details
        console.error(`[SupabaseUserRepository] Error finding user by clerk_id:`, error);
        throw new Error(`Database error when finding user by clerk_id: ${error.message}`);
      }
      
      if (!data) {
        return null;
      }
      
      return this.mapToUser(data);
    } catch (error) {
      console.error(`[SupabaseUserRepository] Unexpected error finding user by clerk_id:`, error);
      
      // For any unexpected error, if not already switched, try admin access
      if (!this.fallbackToAdmin) {
        this.fallbackToAdmin = true;
        this.supabasePromise = Promise.resolve(getSupabaseAdmin());
        return this.findByClerkId(clerkId);
      }
      
      throw error;
    }
  }

  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    const supabase = await this.supabasePromise;
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error(`[SupabaseUserRepository] Error finding user by email:`, error);
      throw new Error(`Database error when finding user by email: ${error.message}`);
    }
    
    if (!data) {
      return null;
    }
    
    return this.mapToUser(data);
  }

  /**
   * Create a new user
   */
  async create(data: CreateUserDTO): Promise<User> {
    
    try {
      // Prepare data for insertion
      const insertData: Record<string, unknown> = {
        email: data.email,
        clerk_id: data.clerkId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // If ID is provided, use it
      if (data.id) {
        insertData.id = data.id;
      }
      
   
      const supabase = await this.supabasePromise;
      const { data: newUser, error } = await supabase
        .from('users')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error(`[SupabaseUserRepository] Error creating user:`, error);
        throw new Error(`Failed to create user: ${error.message}`);
      }
      
      if (!newUser) {
        console.error(`[SupabaseUserRepository] User created but no data returned`);
        throw new Error(`User created but no data returned`);
      }
      
      return this.mapToUser(newUser);
    } catch (error) {
      console.error(`[SupabaseUserRepository] Unexpected error creating user:`, error);
      throw error;
    }
  }

  /**
   * Update an existing user
   */
  async update(id: string, data: UpdateUserDTO): Promise<User> {
    const updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    };

    if (data.email !== undefined) updateData.email = data.email;
    if (data.clerkId !== undefined) updateData.clerk_id = data.clerkId;

    const supabase = await this.supabasePromise;
    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`[SupabaseUserRepository] Error updating user:`, error);
      throw new Error(`Failed to update user: ${error.message}`);
    }
    
    if (!updatedUser) {
      throw new Error(`Failed to update user: No data returned`);
    }
    
    return this.mapToUser(updatedUser);
  }

  /**
   * Delete a user
   */
  async delete(id: string): Promise<boolean> {
    const supabase = await this.supabasePromise;
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`[SupabaseUserRepository] Error deleting user:`, error);
      return false;
    }
    
    return true;
  }

  /**
   * Find all users
   */
  async findAll(): Promise<User[]> {
    const supabase = await this.supabasePromise;
    const { data, error } = await supabase
      .from('users')
      .select('*');

    if (error) {
      console.error(`[SupabaseUserRepository] Error finding all users:`, error);
      throw new Error(`Failed to find all users: ${error.message}`);
    }
    
    if (!data) return [];
    
    return data.map(this.mapToUser);
  }

  /**
   * Map database record to User DTO
   */
  private mapToUser(data: Record<string, unknown>): User {
    return {
      id: data.id as string,
      email: data.email as string,
      clerkId: data.clerk_id as string,
      createdAt: data.created_at ? new Date(data.created_at as string) : new Date(),
      updatedAt: data.updated_at ? new Date(data.updated_at as string) : new Date()
    };
  }
}