/* eslint-disable react-hooks/exhaustive-deps */
import { useHighlightAncestorPath } from "@/hooks/useHighlightAncestorPath";
import useCurrentNode from "@/hooks/useCurrentNode";
import { useEffect } from "react";
import { useGraph } from "@/contexts/GraphContext";

export default function useCurrentNodeLogic() {
  const { nodes, edges } = useGraph();
  const { currentNodeId } = useCurrentNode();
  const { highlightPath } = useHighlightAncestorPath();
  useEffect(() => {
    // Сначала сбрасываем текущую подсветку
    // resetHighlight(nodes, edges);

    if (currentNodeId) {
      const data = nodes.find((n) => n.id === currentNodeId)?.data;
      if (data) {
        highlightPath(currentNodeId, nodes, edges);
      }
    }
  }, [currentNodeId, edges.length]);
}
