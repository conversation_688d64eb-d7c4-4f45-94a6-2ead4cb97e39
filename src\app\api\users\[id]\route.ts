/* eslint-disable no-console */

import { NextRequest, NextResponse } from "next/server";
import { RepositoryFactory } from "@/repositories";
import { UpdateUserDTO } from "@/types/dto";

interface Params {
  params: {
    userId: string;
  };
}

/**
 * GET /api/users/[userId]
 * Get a user by ID
 */
export async function GET(req: NextRequest, { params }: Params) {
  try {
    const { userId } = params;
    const userRepository = RepositoryFactory.getUserRepository();

    const user = await userRepository.findById(userId);

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error(`[API] Error fetching user ${params.userId}:`, error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 },
    );
  }
}

/**
 * PATCH /api/users/[userId]
 * Update a user
 */
export async function PATCH(req: NextRequest, { params }: Params) {
  try {
    const { userId } = params;
    const data: UpdateUserDTO = await req.json();

    const userRepository = RepositoryFactory.getUserRepository();

    // Check if user exists
    const existingUser = await userRepository.findById(userId);
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const updatedUser = await userRepository.update(userId, data);

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error(`[API] Error updating user ${params.userId}:`, error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/users/[userId]
 * Delete a user
 */
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const { userId } = params;
    const userRepository = RepositoryFactory.getUserRepository();

    // Check if user exists
    const existingUser = await userRepository.findById(userId);
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    await userRepository.delete(userId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`[API] Error deleting user ${params.userId}:`, error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 },
    );
  }
}
