import { TagDTO, CreateTagDTO, UpdateTagDTO } from "@/types/dto";

/**
 * Interface for Tag repository
 * Defines methods for interacting with tag data
 */
export interface ITagRepository {
  /**
   * Find a tag by ID
   * @param id - The tag ID
   * @returns The tag or null if not found
   */
  findById(id: string): Promise<TagDTO | null>;
  
  /**
   * Find all tags for a specific user
   * @param userId - The user ID
   * @returns Array of tags
   */
  findByUserId(userId: string): Promise<TagDTO[]>;
  
  /**
   * Find all system tags (not associated with a specific user)
   * @returns Array of system tags
   */
  findSystemTags(): Promise<TagDTO[]>;
  
  /**
   * Create a new tag
   * @param data - The tag data
   * @returns The created tag
   */
  create(data: CreateTagDTO): Promise<TagDTO>;
  
  /**
   * Update an existing tag
   * @param id - The tag ID
   * @param data - The updated tag data
   * @returns The updated tag
   */
  update(id: string, data: UpdateTagDTO): Promise<TagDTO>;
  
  /**
   * Delete a tag
   * @param id - The tag ID
   * @returns True if successful, false otherwise
   */
  delete(id: string): Promise<boolean>;
}
