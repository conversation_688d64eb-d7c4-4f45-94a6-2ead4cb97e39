"use client";

import React, { useEffect, useRef, useCallback } from "react";

interface AutoScrollProps {
  /**
   * Whether to trigger scrolling
   */
  shouldScroll: boolean;
  /**
   * Optional behavior for scrolling
   */
  behavior?: ScrollBehavior;
  /**
   * Optional threshold for auto-scrolling (in pixels)
   * If the user is within this distance from the bottom, auto-scroll will engage
   */
  threshold?: number;
}

/**
 * Component that automatically scrolls to its position when shouldScroll changes
 * or when new content is added to the chat
 */
export const AutoScroll: React.FC<AutoScrollProps> = ({
  shouldScroll,
  behavior = "smooth",
  threshold = 100,
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isNearBottomRef = useRef(true);

  // Find the scrollable container (parent element with overflow-y: auto/scroll)
  const findScrollableParent = useCallback(() => {
    if (scrollRef.current) {
      let parent = scrollRef.current.parentElement;
      while (parent) {
        const overflowY = window.getComputedStyle(parent).overflowY;
        if (overflowY === 'auto' || overflowY === 'scroll') {
          containerRef.current = parent as HTMLDivElement;
          break;
        }
        parent = parent.parentElement;
      }
    }
  }, []);

  // Initialize scrollable container reference
  useEffect(() => {
    findScrollableParent();
    
    // Create a ResizeObserver to detect DOM changes that might affect scrolling
    const resizeObserver = new ResizeObserver(() => {
      findScrollableParent();
    });
    
    // Observe the current element
    if (scrollRef.current) {
      resizeObserver.observe(scrollRef.current);
    }
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [findScrollableParent]);

  // Check if user is near bottom before content changes
  useEffect(() => {
    const checkIfNearBottom = () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        isNearBottomRef.current = scrollHeight - scrollTop - clientHeight <= threshold;
      }
    };

    // Add scroll event listener to track user's scroll position
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', checkIfNearBottom);
      // Initial check
      checkIfNearBottom();
      return () => container.removeEventListener('scroll', checkIfNearBottom);
    }
  }, [threshold]);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({
        behavior,
        block: "end",
      });
    }
  }, [behavior]);

  // Auto-scroll when content changes if user is near bottom
  const autoScrollIfNeeded = useCallback(() => {
    if (isNearBottomRef.current) {
      scrollToBottom();
    }
  }, [scrollToBottom]);

  // Set up a mutation observer to detect content changes
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new MutationObserver(() => {
      autoScrollIfNeeded();
    });

    observer.observe(containerRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return () => observer.disconnect();
  }, [autoScrollIfNeeded]);

  // Handle explicit scroll triggers
  useEffect(() => {
    if (shouldScroll) {
      scrollToBottom();
    }
  }, [shouldScroll, scrollToBottom]);

  return <div ref={scrollRef} />;
};
