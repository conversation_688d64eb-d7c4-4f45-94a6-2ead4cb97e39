/* eslint-disable no-console */
// noinspection ExceptionCaughtLocallyJS

"use client";

import { useAuth, useClerk } from "@clerk/nextjs";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { GRAPHS_PAGE, ROUTES, API, UI } from "@/lib/constants";
import { GraphCard } from "@/components/graphs/GraphCard";
import { DeleteGraphModal } from "@/components/modals/DeleteGraphModal";

interface Graph {
  id: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export default function GraphsPage(): JSX.Element {
  const { isLoaded, userId } = useAuth();
  const { signOut } = useClerk();
  const [graphs, setGraphs] = useState<Graph[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSigningOut, setIsSigningOut] = useState<boolean>(false);
  const [graphToDelete, setGraphToDelete] = useState<Graph | null>(null);
  const router = useRouter();

  // Handle sign out
  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      // Clerk will automatically redirect to the home page after sign out
    } catch (error) {
      console.error("Error signing out:", error);
      setIsSigningOut(false);
    }
  };

  // Fetch user's graphs
  const fetchGraphs = useCallback(async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`${API.GRAPHS}`);
      
      if (response.status === 401) {
        router.push(ROUTES.LOGIN);
        return;
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error loading graphs.:", errorText);
        return;
      }
      
      const data = await response.json();

      // Convert dates from strings to Date objects
      const formattedGraphs = data.map((graph: {
        id: string;
        title: string;
        description?: string;
        createdAt?: string;
        updatedAt?: string;
        created_at?: string;
        updated_at?: string;
      }) => ({
        ...graph,
        createdAt: new Date(graph.createdAt || graph.created_at || new Date()),
        updatedAt: new Date(graph.updatedAt || graph.updated_at || new Date()),
      }));
      
      setGraphs(formattedGraphs);
    } catch (error) {
      console.error("Error loading graphs:", error);
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  // Create a new graph
  const createGraph = async () => {
    try {
      const response = await fetch(`${API.GRAPHS}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: "New graph",
          description: "",
        }),
      });

      if (response.ok) {
        const newGraph = await response.json();
        router.push(`${ROUTES.GRAPH}/${newGraph.id}`);
      } else {
        console.error("Error creating graph.", await response.text());
      }
    } catch (error) {
      console.error("Error creating graph.", error);
    }
  };

  // Rename the graph
  const handleRename = async (id: string, newTitle: string) => {
    try {
      const response = await fetch(`${API.GRAPHS}/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: newTitle,
        }),
      });

      if (response.ok) {
        // Update the list of graphs
        setGraphs(prevGraphs => 
          prevGraphs.map(graph => 
            graph.id === id ? { ...graph, title: newTitle, updatedAt: new Date() } : graph
          )
        );
      } else {
        console.error("Error renaming the graph.", await response.text());
        throw new Error("Error renaming the graph.");
      }
    } catch (error) {
      console.error("Error renaming the graph.", error);
      throw error;
    }
  };

  // Duplicate the graph
  const handleDuplicate = async (id: string) => {
    try {
      const response = await fetch(`${API.GRAPHS}/${id}/duplicate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        // const newGraph = await response.json();
        
        // Update the list of graphs
        await fetchGraphs();
      } else {
        console.error("Error duplicating the graph.", await response.text());
        throw new Error("Error duplicating the graph");
      }
    } catch (error) {
      console.error("Error duplicating the graph: ", error);
      throw error;
    }
  };

  // Delete the graph
  const handleDelete = async (id: string) => {
    const graphToDelete = graphs.find(graph => graph.id === id);
    if (graphToDelete) {
      setGraphToDelete(graphToDelete);
    }
  };

  // Confirm the deletion of the graph
  const confirmDelete = async () => {
    if (!graphToDelete) return;
    
    try {
      const response = await fetch(`${API.GRAPHS}/${graphToDelete.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Remove the graph from the list
        setGraphs(prevGraphs => prevGraphs.filter(graph => graph.id !== graphToDelete.id));
        setGraphToDelete(null);
      } else {
        console.error("Error deleting the graph:", await response.text());
        throw new Error("Error deleting the graph");
      }
    } catch (error) {
      console.error("Error deleting the graph:", error);
      throw error;
    }
  };

  useEffect(() => {
    if (isLoaded && userId) {
      fetchGraphs();
    }
  }, [isLoaded, userId, fetchGraphs]);

  if (!isLoaded) {
    return <div className="flex min-h-screen items-center justify-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">{GRAPHS_PAGE.TITLE}</h1>
        <div className="flex space-x-4">
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            onClick={createGraph}
          >
            {GRAPHS_PAGE.CREATE_NEW}
          </button>
          <button
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded flex items-center"
            onClick={handleSignOut}
            disabled={isSigningOut}
          >
            {isSigningOut ? "Signing out..." : UI.BUTTONS.SIGN_OUT}
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-400">{GRAPHS_PAGE.LOADING}</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {graphs.length > 0 ? (
            graphs.map((graph) => (
              <GraphCard
                key={graph.id}
                graph={graph}
                onDelete={handleDelete}
                onRename={handleRename}
                onDuplicate={handleDuplicate}
              />
            ))
          ) : (
            <div className="col-span-3 text-center py-10">
              <p className="text-gray-500 mb-4">{"You don't have any graphs yet."}</p>
              <button
                onClick={createGraph}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                Create First Graph
              </button>
            </div>
          )}
        </div>
      )}
      
      {graphToDelete && (
        <DeleteGraphModal
          isOpen={!!graphToDelete}
          onClose={() => setGraphToDelete(null)}
          onConfirm={confirmDelete}
          graphTitle={graphToDelete.title}
        />
      )}
    </div>
  );
}
