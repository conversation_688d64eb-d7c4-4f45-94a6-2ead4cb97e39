import { getSupabaseClient } from "@/lib/supabase-client";
import { INodeMetadataRepository } from "@/repositories";
import { NodeMetadata, CreateNodeMetadataDTO, UpdateNodeMetadataDTO } from "@/types/dto";

/**
 * Supabase implementation of the Node Metadata repository
 */
export class SupabaseNodeMetadataRepository implements INodeMetadataRepository {
  private supabase;

  constructor() {
    this.supabase = getSupabaseClient();
  }

  /**
   * Find metadata for a specific node
   */
  async findByNodeId(nodeId: string): Promise<NodeMetadata | null> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_node_metadata')
      .select('*')
      .eq('node_id', nodeId)
      .single();

    if (error || !data) return null;

    return this.mapToNodeMetadata(data);
  }

  /**
   * Create metadata for a node
   */
  async create(data: CreateNodeMetadataDTO): Promise<NodeMetadata> {
    const metadataData = {
      node_id: data.nodeId,
      tags: data.tags || [],
      embedding: data.embedding || null,
      additional_data: data.additionalData || {}
    };

    const client = await this.supabase;
    const { data: newMetadata, error } = await client
      .from('chat_node_metadata')
      .insert(metadataData)
      .select()
      .single();

    if (error || !newMetadata) {
      throw new Error(`Failed to create node metadata: ${error?.message}`);
    }

    return this.mapToNodeMetadata(newMetadata);
  }

  /**
   * Update metadata for a node
   */
  async update(nodeId: string, data: UpdateNodeMetadataDTO): Promise<NodeMetadata> {
    const updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString(),
    };

    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.embedding !== undefined) updateData.embedding = data.embedding;
    if (data.additionalData !== undefined) updateData.additional_data = data.additionalData;

    const client = await this.supabase;
    const { data: updatedMetadata, error } = await client
      .from('chat_node_metadata')
      .update(updateData)
      .eq('node_id', nodeId)
      .select()
      .single();

    if (error || !updatedMetadata) {
      throw new Error(`Failed to update node metadata: ${error?.message}`);
    }

    return this.mapToNodeMetadata(updatedMetadata);
  }

  /**
   * Delete metadata for a node
   */
  async delete(nodeId: string): Promise<boolean> {
    const client = await this.supabase;
    const { error } = await client
      .from('chat_node_metadata')
      .delete()
      .eq('node_id', nodeId);

    return !error;
  }

  /**
   * Add a tag to a node
   */
  async addTag(nodeId: string, tag: string): Promise<NodeMetadata> {
    // Получаем текущие метаданные
    const client = await this.supabase;
    const { data: metadata } = await client
      .from('chat_node_metadata')
      .select('*')
      .eq('node_id', nodeId)
      .single();

    if (!metadata) {
      // Если метаданных нет, создаем новые
      return this.create({
        nodeId,
        tags: [tag]
      });
    }

    // Проверяем, есть ли уже такой тег
    const currentTags = metadata.tags || [];
    if (currentTags.includes(tag)) {
      return this.mapToNodeMetadata(metadata);
    }

    // Добавляем тег и обновляем метаданные
    const newTags = [...currentTags, tag];
    const { data: updatedMetadata, error } = await client
      .from('chat_node_metadata')
      .update({ tags: newTags, updated_at: new Date().toISOString() })
      .eq('node_id', nodeId)
      .select()
      .single();

    if (error || !updatedMetadata) {
      throw new Error(`Failed to add tag to node: ${error?.message}`);
    }

    return this.mapToNodeMetadata(updatedMetadata);
  }

  /**
   * Remove a tag from a node
   */
  async removeTag(nodeId: string, tag: string): Promise<NodeMetadata> {
    // Получаем текущие метаданные
    const client = await this.supabase;
    const { data: metadata } = await client
      .from('chat_node_metadata')
      .select('*')
      .eq('node_id', nodeId)
      .single();

    if (!metadata) {
      throw new Error(`Node metadata not found for node: ${nodeId}`);
    }

    // Удаляем тег из списка
    const currentTags = metadata.tags || [];
    const newTags = currentTags.filter((t: string) => t !== tag);

    // Обновляем метаданные
    const { data: updatedMetadata, error } = await client
      .from('chat_node_metadata')
      .update({ tags: newTags, updated_at: new Date().toISOString() })
      .eq('node_id', nodeId)
      .select()
      .single();

    if (error || !updatedMetadata) {
      throw new Error(`Failed to remove tag from node: ${error?.message}`);
    }

    return this.mapToNodeMetadata(updatedMetadata);
  }

  /**
   * Update the embedding vector for a node
   */
  async updateEmbedding(nodeId: string, embedding: number[]): Promise<NodeMetadata> {
    const client = await this.supabase;
    const { data: updatedMetadata, error } = await client
      .from('chat_node_metadata')
      .update({ embedding, updated_at: new Date().toISOString() })
      .eq('node_id', nodeId)
      .select()
      .single();

    if (error || !updatedMetadata) {
      throw new Error(`Failed to update node embedding: ${error?.message}`);
    }

    return this.mapToNodeMetadata(updatedMetadata);
  }

  /**
   * Map Supabase data to NodeMetadata
   */
  private mapToNodeMetadata(data: Record<string, unknown>): NodeMetadata {
    return {
      id: data.id as string,
      nodeId: data.node_id as string,
      tags: (data.tags as string[]) || [],
      embedding: data.embedding as number[] || undefined,
      additionalData: data.additional_data as Record<string, unknown> || {},
      createdAt: new Date(data.created_at as string),
      updatedAt: new Date(data.updated_at as string),
    };
  }
}
