"use client";

import React, { useEffect, useRef, useCallback } from "react";
import {
  ReactFlow,
  MiniMap,
  Background,
  useReact<PERSON>low,
  NodeMouse<PERSON><PERSON>ler,
  EdgeMouseHandler
} from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import "@xyflow/react/dist/style.css";
import "@/styles/nodeStyles.css"; // Импортируем наши стили для узлов
import { useGraphConnections } from "@/hooks/useGraphConnections";
import { useSelectNode } from "@/hooks/useSelectNode";
import { useVisibleNodes } from "@/hooks/useVisibleNodes";
import useCurrentNodeLogic from "./hooks/useCurrentNodeLogic";
import useDimensionLogic from "./hooks/useDimensionLogic";
import useVisibilityLogic from "./hooks/useVisibilityLogic";
import MiniMapNode from "./MiniMap";
import { useUnselectNode } from "@/hooks/useUnSelectNode";
import { PreviewSelected } from "./PreviewSelected";
import { NodeContextMenu } from "./NodeContextMenu";
import { useNodeContextMenu } from "@/hooks/useNodeContextMenu";
import { EdgeContextMenu } from "./EdgeContextMenu";
import { useEdgeContextMenu } from "@/hooks/useEdgeContextMenu";
import useSelectEdge from "@/hooks/useSelectEdge";
import { CustomNode } from './CustomNode';
import { PreviewSelectedEdge } from "./PreviewSelectedEdge";

// Define the ref interface
export interface GraphComponentHandle {
  zoomIn: () => void;
  zoomOut: () => void;
  fitView: () => void;
  toggleInteractivity: (value: boolean) => void;
}

export const GraphComponent = () => {
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    bgColor,
    isInteractive,
    isPanEnabled,
    setIsPanEnabled,
    reactFlowInstanceRef,
    defaultViewport,
    backgroundVariant,
    selectedNodeId,
  } = useGraph();
  const reactFlow = useReactFlow();

  // We need these variables for the GraphDimensionsContext to work properly

  const { onConnect, onConnectStart, onConnectEnd } = useGraphConnections();
  const selectNode = useSelectNode();
  const ensureNodesVisible = useVisibleNodes();
  const containerRef = useRef<HTMLDivElement>(null);
  const unselectNode = useUnselectNode();
  const { 
    contextMenu, 
    handleNodeContextMenu, 
    closeContextMenu, 
    handleDeleteFromContextMenu,
    handleToggleInclusionFromContextMenu,
    reactFlowRef
  } = useNodeContextMenu();
  
  const {
    edgeContextMenu,
    handleEdgeContextMenu,
    closeEdgeContextMenu,
    handleDeleteEdgeFromContextMenu,
    handleToggleBlockEdgeFromContextMenu,
    handleSetTagFilterFromContextMenu,
    handleToggleTagInEdgeFilter
  } = useEdgeContextMenu();
  
  // Edge selection functionality
  const { onEdgeClick, deselectEdges } = useSelectEdge();
  
  // Получаем ID выделенного ребра
  const selectedEdgeId = edges.find(edge => edge.selected)?.id;
  
  // Модифицированный обработчик клика по узлу, который снимает выделение с ребер
  const handleNodeClick: NodeMouseHandler = useCallback((event, node) => {
    // Сначала снимаем выделение со всех ребер
    deselectEdges();
    // Затем выделяем узел
    selectNode(event, node);
  }, [deselectEdges, selectNode]);
  
  // Модифицированный обработчик клика по ребру
  const handleEdgeClick: EdgeMouseHandler = useCallback((event, edge) => {
    // Сначала снимаем выделение с узла
    if (selectedNodeId) {
      unselectNode();
    }
    // Затем обрабатываем клик по ребру
    onEdgeClick(event, edge);
  }, [selectedNodeId, unselectNode, onEdgeClick]);
  
  // Модифицированный обработчик клика по пустому месту
  const handlePaneClick = useCallback(() => {
    unselectNode();
    deselectEdges();
  }, [unselectNode, deselectEdges]);
  
  useCurrentNodeLogic();
  useDimensionLogic(containerRef);
  useVisibilityLogic();

  // Store the ReactFlow instance in the context ref
  useEffect(() => {
    if (reactFlow && reactFlowInstanceRef) {
      reactFlowInstanceRef.current = reactFlow;
    }
  }, [reactFlow, reactFlowInstanceRef]);

  // Handle wheel button (middle mouse button) events
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    // Check if it's the middle mouse button (wheel)
    if (event.button === 1) {
      setIsPanEnabled(true);
    }
  }, [setIsPanEnabled]);

  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    // Check if it's the middle mouse button (wheel)
    if (event.button === 1) {
      setIsPanEnabled(false);
    }
  }, [setIsPanEnabled]);

  // Add global event listeners for wheel button
  useEffect(() => {
    const handleGlobalMouseUp = (event: MouseEvent) => {
      if (event.button === 1) {
        setIsPanEnabled(false);
      }
    };

    // Add global event listener to catch mouse up even if it happens outside the container
    window.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [setIsPanEnabled]);

  // Check if there are any nodes to display
  const hasNodes = nodes.length > 0;

  return (
    <div 
      ref={containerRef} 
      className="w-full h-full"
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      <div style={{ width: "100%", height: "100%", background: bgColor }}>
        <ReactFlow
          ref={reactFlowRef}
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onConnectStart={onConnectStart}
          onConnectEnd={onConnectEnd}
          onNodeClick={handleNodeClick}
          onNodeContextMenu={handleNodeContextMenu}
          onEdgeContextMenu={handleEdgeContextMenu}
          onEdgeClick={handleEdgeClick}
          onPaneClick={handlePaneClick}
          onNodeDragStop={ensureNodesVisible}
          defaultViewport={defaultViewport}
          nodeTypes={{ custom: CustomNode }}
          fitView
          fitViewOptions={{
            minZoom: 1,
            maxZoom: 1.5,
          }}
          style={{
            width: "100%",
            height: "100%",
            color: "black",
            backgroundColor: "white",
          }}
          nodesDraggable={isInteractive}
          nodesConnectable={isInteractive}
          elementsSelectable={isInteractive}
          panOnDrag={isPanEnabled ? true : [1, 2]}
          zoomOnScroll={true}
          proOptions={{ hideAttribution: true }}
        >
          {hasNodes && (
            <MiniMap
              nodeStrokeWidth={1}
              nodeBorderRadius={2}
              nodeColor="#FFFFFF"
              maskColor="rgba(0, 0, 0, 0.02)"
              nodeComponent={(props) => (
                <MiniMapNode {...props} />
              )}
              style={{
                position: "absolute",
                bottom: 10,
                right: 10,
                backgroundColor: "#4B5563" /* bg-gray-600 in Tailwind */,
                border: "1px solid #374151",
                borderRadius: "0.5rem",
                boxShadow:
                  "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.3)",
                padding: "8px",
              }}
              pannable={true}
              zoomable={true}
            />
          )}
          <Background
            variant={backgroundVariant}
            gap={12}
            size={1}
            color="#374151"
          />

          {/* Отображаем информацию о выделенном узле или ребре */}
          {selectedNodeId && (
            <PreviewSelected selectedNodeId={selectedNodeId} nodes={nodes} />
          )}
          
          {selectedEdgeId && !selectedNodeId && (
            <PreviewSelectedEdge selectedEdgeId={selectedEdgeId} edges={edges} />
          )}
          
          {contextMenu.show && (
            <NodeContextMenu
              x={contextMenu.x}
              y={contextMenu.y}
              nodeId={contextMenu.nodeId}
              node={contextMenu.node as import('@xyflow/react').Node}
              onDelete={handleDeleteFromContextMenu}
              onToggleInclusion={handleToggleInclusionFromContextMenu}
              onClose={closeContextMenu}
            />
          )}
          
          {edgeContextMenu.show && (
            <EdgeContextMenu
              x={edgeContextMenu.x}
              y={edgeContextMenu.y}
              edgeId={edgeContextMenu.edgeId}
              onDelete={handleDeleteEdgeFromContextMenu}
              onClose={closeEdgeContextMenu}
              onToggleBlock={handleToggleBlockEdgeFromContextMenu}
              onSetTagFilter={handleSetTagFilterFromContextMenu}
              onToggleTag={handleToggleTagInEdgeFilter}
              isBlocked={!!edgeContextMenu.edge?.data?.isBlocked}
              tagFilter={edgeContextMenu.edge && edgeContextMenu.edge.data && Array.isArray(edgeContextMenu.edge.data.tagFilter) ? edgeContextMenu.edge.data.tagFilter : []}
            />
          )}
        </ReactFlow>

        {/* Debug component to display current node information */}
        {/* <CurrentNodeDebug /> */}
      </div>
    </div>
  );
};

GraphComponent.displayName = "GraphComponent";
