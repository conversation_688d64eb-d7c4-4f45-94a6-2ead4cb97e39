import React, { ReactNode } from "react";

interface ControlButtonProps {
  onClick: () => void;
  disabled?: boolean;
  title: string;
  icon: ReactNode;
  className?: string;
  disabledStyles?: string;
}

/**
 * Reusable button component for all control panels
 */
export const ControlButton: React.FC<ControlButtonProps> = ({
  onClick,
  disabled = false,
  title,
  icon,
  className = "",
  disabledStyles = !disabled
  ? "bg-indigo-600 hover:bg-indigo-700"
  : "bg-gray-600",
}) => {
  // Define base styles that will be applied to all buttons
  const baseStyles =
    "flex items-center justify-center h-7 w-7 rounded text-white";

  // Define default styles for active and inactive buttons
  const defaultStyles = disabledStyles;

  // Combine styles, giving priority to user styles (className)
  const buttonStyles = className
    ? `${className} ${baseStyles}`
    : `${baseStyles} ${defaultStyles}`;

   return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={buttonStyles}
      title={title}
    >
      {icon}
    </button>
  );
};
