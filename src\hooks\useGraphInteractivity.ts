import { useState, useCallback, useMemo, MutableRefObject } from "react";
import { ReactFlowInstance } from "@xyflow/react";

/**
 * Custom hook to manage graph interactivity and zoom functions
 * Provides state and functions for controlling graph interactivity and zoom
 */
export const useGraphInteractivity = (
  reactFlowInstanceRef: MutableRefObject<ReactFlowInstance | null>,
) => {
  // State for interactivity
  const [isInteractive, setIsInteractive] = useState<boolean>(true);
  
  // State for panning specifically
  const [isPanEnabled, setIsPanEnabled] = useState<boolean>(false);

  // Zoom in function
  const zoomIn = useCallback((): void => {
    if (reactFlowInstanceRef.current) {
      reactFlowInstanceRef.current.zoomIn();
    }
  }, [reactFlowInstanceRef]);

  // Zoom out function
  const zoomOut = useCallback((): void => {
    if (reactFlowInstanceRef.current) {
      reactFlowInstanceRef.current.zoomOut();
    }
  }, [reactFlowInstanceRef]);

  // Fit view function
  const fitView = useCallback((): void => {
    if (reactFlowInstanceRef.current) {
      reactFlowInstanceRef.current.fitView();
    }
  }, [reactFlowInstanceRef]);

  // Toggle interactivity function
  const toggleInteractivity = useCallback((): void => {
    setIsInteractive((prev) => !prev);
  }, []);

  // Toggle pan function
  const togglePan = useCallback((): void => {
    setIsPanEnabled((prev) => !prev);
  }, []);

  return useMemo(
    () => ({
      isInteractive,
      setIsInteractive,
      isPanEnabled,
      setIsPanEnabled,
      zoomIn,
      zoomOut,
      fitView,
      toggleInteractivity,
      togglePan,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isInteractive, isPanEnabled],
  );
};
