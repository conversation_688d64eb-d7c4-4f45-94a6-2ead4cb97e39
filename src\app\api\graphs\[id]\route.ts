/* eslint-disable no-console */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { RepositoryFactory } from "@/repositories";
import { UpdateGraphDTO } from "@/types/dto";

interface Params {
  params: {
    id: string;
  };
}

/**
 * GET /api/graphs/[id]
 * Get a graph by ID
 */
export async function GET(req: NextRequest, { params }: Params) {
  try {
    // Get the current user from Clerk
    const authObject = await auth();
    const { userId: clerkUserId, sessionId } = authObject;

    if (!clerkUserId) {
      console.error(
        `[API][graphs/id] User not authenticated for graph ${params.id}`
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the session ID from either auth or request headers
    const headerSessionId = req.headers.get("x-clerk-session-id");
    const effectiveSessionId = sessionId || headerSessionId;

    // Set the session ID for JWT refresh if needed
    if (effectiveSessionId) {
      RepositoryFactory.setSessionId(effectiveSessionId);
    }

    const { id } = params;
    const graphRepository = RepositoryFactory.getGraphRepository();

    // Find the graph by ID
    const graph = await graphRepository.findById(id);

    if (!graph) {
      console.error(`[API][graphs/id] Graph with ID ${id} not found`);
      return NextResponse.json({ error: "Graph not found" }, { status: 404 });
    }

    // Get the user repository
    const userRepository = RepositoryFactory.getUserRepository();

    // Find the user by Clerk ID
    const user = await userRepository.findByClerkId(clerkUserId);

    if (!user) {
      console.error(
        `[API][graphs/id] User with Clerk ID ${clerkUserId} not found`
      );

      // If this is a server-side request, we don't need to check ownership
      if (typeof window === "undefined") {
        return NextResponse.json(graph);
      }

      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify that the user owns the graph
    if (graph.userId !== user.id) {
      // If this is a server-side request, we don't need to check ownership
      if (typeof window === "undefined") {
        return NextResponse.json(graph);
      }

      console.error(
        `[API][graphs/id] Graph does not belong to user. Graph owner: ${graph.userId}, User: ${user.id}`
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    return NextResponse.json(graph);
  } catch (error) {
    console.error(
      `[API][graphs/id] Error retrieving graph ${params.id}:`,
      error
    );

    // If the error is JWT-related, return 401
    if (
      error instanceof Error &&
      (error.message.includes("JWT") ||
        error.message.includes("token") ||
        error.message.includes("auth"))
    ) {
      console.error(`[API][graphs/id] JWT authentication error detected`);
      return NextResponse.json(
        { error: "Authentication error. Please try logging out and in again." },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch graph" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/graphs/[id]
 * Update a graph
 */
export async function PATCH(req: NextRequest, { params }: Params) {
  try {
    // Get the current user from Clerk
    const authObject = await auth();
    const { userId: clerkUserId, sessionId } = authObject;

    if (!clerkUserId) {
      console.error(
        `[API][graphs/id] Unauthorized access attempt for graph ${params.id}`
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the session ID from either auth or request headers
    const headerSessionId = req.headers.get("x-clerk-session-id");
    const effectiveSessionId = sessionId || headerSessionId;

    // Set the session ID for JWT refresh if needed
    if (effectiveSessionId) {
      RepositoryFactory.setSessionId(effectiveSessionId);
    }

    const { id } = params;

    const data: UpdateGraphDTO = await req.json();

    const graphRepository = RepositoryFactory.getGraphRepository();

    // Check if graph exists
    const existingGraph = await graphRepository.findById(id);

    if (!existingGraph) {
      console.error(`[API][graphs/id] Graph with ID ${id} not found`);
      return NextResponse.json({ error: "Graph not found" }, { status: 404 });
    }

    // Get the user repository
    const userRepository = RepositoryFactory.getUserRepository();

    // Find the user by Clerk ID
    const user = await userRepository.findByClerkId(clerkUserId);

    if (!user) {
      console.error(
        `[API][graphs/id] User with Clerk ID ${clerkUserId} not found`
      );
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the graph belongs to the user
    if (existingGraph.userId !== user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const updatedGraph = await graphRepository.update(id, data);

    return NextResponse.json(updatedGraph);
  } catch (error) {
    console.error(`[API][graphs/id] Error updating graph ${params.id}:`, error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    // If the error is JWT-related, return 401
    if (
      error instanceof Error &&
      (error.message.includes("JWT") ||
        error.message.includes("token") ||
        error.message.includes("auth"))
    ) {
      console.error(`[API][graphs/id] JWT authentication error detected`);
      return NextResponse.json(
        { error: "Authentication error. Please try logging out and in again." },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: `Failed to update graph: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/graphs/[id]
 * Delete a graph
 */
export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    // Get the current user from Clerk
    const authObject = await auth();
    const { userId: clerkUserId, sessionId } = authObject;

    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the session ID from either auth or request headers
    const headerSessionId = req.headers.get("x-clerk-session-id");
    const effectiveSessionId = sessionId || headerSessionId;

    // Set the session ID for JWT refresh if needed
    if (effectiveSessionId) {
      RepositoryFactory.setSessionId(effectiveSessionId);
    }

    const { id } = params;
    const graphRepository = RepositoryFactory.getGraphRepository();

    // Check if graph exists
    const existingGraph = await graphRepository.findById(id);
    if (!existingGraph) {
      return NextResponse.json({ error: "Graph not found" }, { status: 404 });
    }

    // Get the user repository
    const userRepository = RepositoryFactory.getUserRepository();

    // Find the user by Clerk ID
    const user = await userRepository.findByClerkId(clerkUserId);
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the graph belongs to the user
    if (existingGraph.userId !== user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await graphRepository.delete(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`[API][graphs/id] Error deleting graph ${params.id}:`, error);

    // If the error is JWT-related, return 401
    if (
      error instanceof Error &&
      (error.message.includes("JWT") ||
        error.message.includes("token") ||
        error.message.includes("auth"))
    ) {
      console.error(`[API][graphs/id] JWT authentication error detected`);
      return NextResponse.json(
        { error: "Authentication error. Please try logging out and in again." },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete graph" },
      { status: 500 }
    );
  }
}
