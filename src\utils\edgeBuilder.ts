import React from 'react';
import { EDGE } from "@/lib/constants";
import { Edge, MarkerType } from "@xyflow/react";

/**
 * Interface for basic edge parameters
 */
interface EdgeParams {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  label?: string;
}

/**
 * Class for creating edges with unified style
 */
export class EdgeBuilder {
  public static defaultMarkerType = MarkerType.Arrow;
  public static defaultMarkerWidth = 8; // Reduced from 10 to 8
  public static defaultMarkerHeight = 8; // Reduced from 10 to 8
  public static defaultMarkerColor = EDGE.COLORS.DEFAULT;
  public static defaultStrokeWidth = EDGE.WIDTHS.DEFAULT;
  public static defaultStrokeColor = EDGE.COLORS.DEFAULT;
  public static isBlocked = false;

  /**
   * Returns color for edge depending on its state
   * @param isBlocked Edge blocking flag
   * @returns Edge color
   */
  public static getEdgeColor(isBlocked: boolean): string {
    return isBlocked ? EDGE.COLORS.BLOCKED : EDGE.COLORS.DEFAULT;
  }

  /**
   * Returns styles for edge depending on its state
   * @param options Edge state options
   * @returns Object with styles and animation
   */
  public static getEdgeStyles(options: {
    isBlocked: boolean;
    isInPath: boolean;
    isSelected: boolean;
    isFiltered?: boolean;
  }): {
    style: {
      stroke: string;
      strokeWidth: number;
      opacity?: number;
      strokeDasharray?: string;
    };
    animated: boolean;
    markerEnd?: {
      type: MarkerType;
      width: number;
      height: number;
      color: string;
    };
  } {
    const { isBlocked, isInPath, isSelected, isFiltered } = options;
    
    // Determine edge color based on its state
    // Priority: blocked > filtered > in path > selected > default
    let strokeColor: string = EDGE.COLORS.DEFAULT;
    if (isBlocked) {
      strokeColor = EDGE.COLORS.BLOCKED;
    } else if (isFiltered) {
      strokeColor = EDGE.COLORS.FILTERED;
    } else if (isInPath) {
      strokeColor = EDGE.COLORS.HIGHLIGHTED;
    } else if (isSelected) {
      strokeColor = EDGE.COLORS.SELECTED;
    }
    
    // Determine edge thickness
    let strokeWidth: number = EDGE.WIDTHS.DEFAULT;
    if (isSelected) {
      strokeWidth = EDGE.WIDTHS.SELECTED;
    } else if (isInPath) {
      strokeWidth = EDGE.WIDTHS.HIGHLIGHTED;
    }
    
    // Determine if animation is needed
    // Blocked edges are never animated
    let animated = false;
    
    if (!isBlocked) {
      if (isSelected && EDGE.ANIMATION.SELECTED) {
        animated = true;
      } else if (isInPath && EDGE.ANIMATION.HIGHLIGHTED) {
        animated = true;
      } else if (isFiltered && EDGE.ANIMATION.FILTERED) {
        animated = true;
      }
    }
    
    // Determine opacity
    // Filtered edges have full opacity
    const opacity = isSelected || isFiltered || (isInPath && !isBlocked) ? 1 : undefined;
    
    // Use dashed line for filtered edges
    const style: {
      stroke: string;
      strokeWidth: number;
      opacity?: number;
      strokeDasharray?: string;
    } = {
      stroke: strokeColor,
      strokeWidth,
      opacity,
    };
    
    // Add dashed line for filtered edges
    if (isFiltered) {
      style.strokeDasharray = '5, 5';
    }
    
    // Determine edge end marker
    const markerEnd = {
      type: this.defaultMarkerType,
      width: this.defaultMarkerWidth,
      height: this.defaultMarkerHeight,
      color: strokeColor,
    };
    
    return {
      style,
      animated,
      markerEnd,
    };
  }

  /**
   * Creates a new edge with given parameters and custom style settings
   * @param params Basic edge parameters
   * @param options Additional options for style customization
   * @returns Edge object
   */
  public static createCustomEdge(
    params: EdgeParams,
    options?: {
      markerType?: MarkerType;
      markerWidth?: number;
      markerHeight?: number;
      markerColor?: string;
      strokeWidth?: number;
      strokeColor?: string;
      animated?: boolean;
      edgeType?: string;
      isBlocked?: boolean;
      tagFilter?: string[]; // Adding option for tag filter
      labelStyle?: React.CSSProperties; // Adding style for label
    },
  ): Edge {
    const isBlocked = options?.isBlocked ?? this.isBlocked;
    const tagFilter = options?.tagFilter ?? []; // Get tag filter from options or use empty array
    const isFiltered = tagFilter.length > 0; // Edge is filtered if there is at least one tag in the filter
    
    // If edge is blocked, use color for blocked edges
    // If edge is filtered, use color for filtered edges
    let edgeColor: string;
    if (isBlocked) {
      edgeColor = EDGE.COLORS.BLOCKED;
    } else if (isFiltered) {
      edgeColor = EDGE.COLORS.FILTERED;
    } else {
      edgeColor = options?.strokeColor ?? this.defaultStrokeColor;
    }
    
    const markerColor: string = isBlocked
      ? EDGE.COLORS.BLOCKED
      : (isFiltered 
          ? EDGE.COLORS.FILTERED 
          : (options?.markerColor ?? this.defaultMarkerColor));
      
    // Determine if animation is needed
    // Blocked edges are never animated
    // Filtered edges are animated if the corresponding option is enabled
    const animated = isBlocked 
      ? false 
      : (isFiltered 
          ? EDGE.ANIMATION.FILTERED 
          : (options?.animated ?? false));
    
    // Determine style
    const style: {
      stroke: string;
      strokeWidth: number;
      strokeDasharray?: string;
    } = {
      strokeWidth: options?.strokeWidth ?? this.defaultStrokeWidth,
      stroke: edgeColor,
    };
    
    // Add dashed line for filtered edges
    if (isFiltered) {
      style.strokeDasharray = '5, 5';
    }
    
    return {
      ...params,
      type: options?.edgeType ?? "default",
      animated,
      label: params.label,
      labelStyle: options?.labelStyle,
      markerEnd: {
        type: options?.markerType ?? this.defaultMarkerType,
        width: options?.markerWidth ?? this.defaultMarkerWidth,
        height: options?.markerHeight ?? this.defaultMarkerHeight,
        color: markerColor,
      },
      style,
      zIndex: 0,
      data: {
        isBlocked,
        tagFilter,
      },
    };
  }
}
