"use client";

import React, { useEffect, useRef } from "react";
import { GraphComponent } from "@/components/graph/GraphComponent";
import { ClientStyleWrapper } from "@/components/providers/ClientStyleWrapper";

interface GraphPanelProps {
  width: number;
  transitionEnabled: boolean;
}

export const GraphPanel: React.FC<GraphPanelProps> = ({
  width,
  transitionEnabled,
}) => {
  const panelRef = useRef<HTMLDivElement>(null);

  // Устанавливаем CSS-переменную для ширины панели
  useEffect(() => {
    if (panelRef.current) {
      panelRef.current.style.width = `${width}%`;
    }
  }, [width]);

  return (
    <ClientStyleWrapper>
      <div
        ref={panelRef}
        className={`flex flex-col border-r border-gray-800 h-full ${
          transitionEnabled ? "transition-all duration-300 ease-out" : ""
        }`}
        style={{ 
          width: `${width}%`,
          height: "100%"
        }}
      >
        <div 
          className="flex-1 relative"
          style={{ 
            width: "100%", 
            height: "100%"
          }}
        >
          <GraphComponent />
        </div>
      </div>
    </ClientStyleWrapper>
  );
};
