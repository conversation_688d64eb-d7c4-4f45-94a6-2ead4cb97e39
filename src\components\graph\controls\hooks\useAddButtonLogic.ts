import React, { useMemo } from "react";
import { useAddNode } from "@/hooks/useAddNode";

export const useAddButtonLogic = () => {
  const addNode = useAddNode();
  
  // Add keydown event listener for Ctrl+N key combination
  const addFn = React.useCallback((event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === '+') {
      event.preventDefault(); // Prevent browser's default behavior
      addNode();
    }
  }, [addNode]);
  
  // Set up event listener
  React.useEffect(() => {
    document.addEventListener("keydown", addFn);
    return () => {
      document.removeEventListener("keydown", addFn);
    };
  }, [addFn]);

  return useMemo(() => ({
    addNode,
    title: "Add Node"
  }), [addNode]);
};