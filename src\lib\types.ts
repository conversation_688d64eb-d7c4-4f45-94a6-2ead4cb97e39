/**
 * Common type definitions for the GraphChat application
 */
import { openai as aiSdkOpenAI } from "@ai-sdk/openai";
import { anthropic as aiSdkAnthropic } from "@ai-sdk/anthropic";
import { google as aiSdkGoogle } from "@ai-sdk/google";
import { perplexity as aiSdkPerplexity } from "@ai-sdk/perplexity";
import { Message } from "ai";

// Chat message role types
export type TMessageSystem = "system";
export type TMessageUser = "user";
export type TMessageAssistant = "assistant";
export type TMessageFunction = "function";
export type TMessageRole =
  | TMessageSystem
  | TMessageUser
  | TMessageAssistant
  | TMessageFunction;
export type TExtendedMessageRole = TMessageRole | TMessageFunction;

// AI configuration types
export type TModelName = string;
export type TTemperature = number;
export type TMaxTokens = number;
export type TTopP = number;
export type TFrequencyPenalty = number;
export type TPresencePenalty = number;

/**
 * AI Configuration interface
 */
// Types for AI providers
export type AIProvider = typeof aiSdkOpenAI | typeof aiSdkAnthropic | typeof aiSdkGoogle | typeof aiSdkPerplexity;

// Type for the model response
export interface AIModelResponse {
  text: string;
}

// Generic type for AI configuration
export interface AIConfigGeneric<T extends AIProvider> {
  model: string;
  temperature?: number;
  maxTokens?: TMaxTokens;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  aiSdk: T;
}

// Type for streaming response options
export interface StreamOptions {
  model: ReturnType<AIProvider>;
  messages: Message[];
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  onFinish?: () => void;
}
/**
 * Basic chat message interface
 */
export interface IChatMessage {
  role: TMessageRole;
  content: string;
}

/**
 * Extended chat message interface with ID
 * This is a separate interface rather than extending IChatMessage
 * to avoid type conflicts with the role property
 */
export interface IExtendedChatMessage {
  id: string;
  role: TExtendedMessageRole;
  content: string;
}
