import React, { useCallback, useEffect, useRef } from 'react';
import { useNodeTags } from '@/hooks/useNodeTags';
import { NodeTag } from '@/utils/nodeBuilder';
// Import the required icons
import { LiaThumbtackSolid } from 'react-icons/lia';
import { FaRegThumbsUp, FaRegThumbsDown, FaRegLightbulb } from 'react-icons/fa';
import { VscError, VscInfo } from 'react-icons/vsc';
import { BiSolidShow, BiSolidHide } from 'react-icons/bi';
import { BsFire } from 'react-icons/bs';

interface NodeTagsManagerProps {
  nodeId: string;
  onClose?: () => void;
}

const NodeTagsManager: React.FC<NodeTagsManagerProps> = ({ 
  nodeId, 
  onClose 
}) => {
  const { 
    getNodeTags, 
    toggleNodeTag, 
    getDefaultTags 
  } = useNodeTags();
  
  const nodeTags = getNodeTags(nodeId);
  const defaultTags = getDefaultTags();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // <PERSON>le clicks outside the tag manager to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        if (onClose) {
          onClose();
        }
      }
    };
    
    // Add the event listener with capture to ensure it fires before other click handlers
    document.addEventListener('mousedown', handleClickOutside, true);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [onClose]);
  
  const handleTagClick = useCallback((tag: NodeTag) => {
    toggleNodeTag(nodeId, tag);
  }, [nodeId, toggleNodeTag]);
  
  // Check if a tag is active on the node
  const isTagActive = useCallback((tagId: string) => {
    return nodeTags.some(t => t.id === tagId);
  }, [nodeTags]);
  
  // Get icon component based on icon name
  const getIconComponent = (iconName: string, color?: string) => {
    const iconProps = { 
      size: 18, 
      color: color || 'currentColor',
      className: "inline-block"
    };
    
    switch (iconName) {
      case 'LiaThumbtackSolid': return <LiaThumbtackSolid {...iconProps} />;
      case 'FaRegThumbsUp': return <FaRegThumbsUp {...iconProps} />;
      case 'FaRegThumbsDown': return <FaRegThumbsDown {...iconProps} />;
      case 'FaRegLightbulb': return <FaRegLightbulb {...iconProps} />;
      case 'VscError': return <VscError {...iconProps} />;
      case 'VscInfo': return <VscInfo {...iconProps} />;
      case 'BiSolidShow': return <BiSolidShow {...iconProps} />;
      case 'BiSolidHide': return <BiSolidHide {...iconProps} />;
      case 'BsFire': return <BsFire {...iconProps} />;
      default: return null;
    }
  };
  
  return (
    <div 
      ref={containerRef}
      className="p-4 bg-gray-800 rounded-md shadow-lg w-[280px] max-h-[400px] overflow-y-auto"
      onClick={(e) => e.stopPropagation()} // Prevent clicks from reaching document
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Tag Manager</h3>
        <button 
          className="text-gray-400 hover:text-white"
          onClick={onClose}
        >
          ✕
        </button>
      </div>
      
      {nodeTags.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm text-gray-400 mb-2 font-medium">Current Tags</h4>
          <div className="flex flex-wrap gap-2">
            {nodeTags.map((tag) => (
              <span
                key={tag.id}
                className="inline-flex items-center justify-center p-2 rounded-full text-xs font-medium cursor-pointer hover:opacity-90 transition-opacity shadow-sm"
                style={{ 
                  backgroundColor: tag.color || '#3b82f6',
                  color: 'white',
                }}
                onClick={() => handleTagClick(tag)}
                title={tag.label || tag.id}
              >
                {getIconComponent(tag.icon)}
              </span>
            ))}
          </div>
        </div>
      )}
      
      <div>
        <h4 className="text-sm text-gray-400 mb-2 font-medium">Available Tags</h4>
        <div className="flex flex-wrap gap-2">
          {defaultTags.map((tag) => (
            <span
              key={tag.id}
              className={`inline-flex items-center justify-center p-2 rounded-full text-xs font-medium cursor-pointer hover:opacity-90 transition-opacity shadow-sm ${
                isTagActive(tag.id) ? 'ring-2 ring-white ring-opacity-70' : 'opacity-80'
              }`}
              style={{ 
                backgroundColor: tag.color || '#3b82f6',
                color: 'white',
              }}
              onClick={() => handleTagClick(tag)}
              title={tag.label || tag.id}
            >
              {getIconComponent(tag.icon)}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NodeTagsManager;
