import React from 'react';

interface MenuButtonProps {
  onClick: (e: React.MouseEvent) => void;
  icon: React.ReactNode;
  label: string;
  className?: string;
  variant?: 'default' | 'danger';
}

export default function MenuButton({
  onClick,
  icon,
  label,
  className = '',
  variant = 'default',
}: MenuButtonProps) {
  const baseClasses = "w-full text-left px-4 py-2 text-sm hover:bg-gray-700 flex items-center";
  const variantClasses = {
    default: "text-gray-300",
    danger: "text-red-400"
  };
  
  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {icon}
      {label}
    </button>
  );
} 