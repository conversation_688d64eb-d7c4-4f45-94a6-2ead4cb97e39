import { GraphDTO as Graph, CreateGraphDTO, UpdateGraphDTO } from "@/types/dto";

/**
 * Interface for Graph repository
 * Defines methods for interacting with graph data
 */
export interface IGraphRepository {
  /**
   * Find a graph by ID
   * @param id - The graph ID
   * @returns The graph or null if not found
   */
  findById(id: string): Promise<Graph | null>;
  
  /**
   * Find all graphs for a specific user
   * @param userId - The user ID
   * @returns Array of graphs
   */
  findByUserId(userId: string): Promise<Graph[]>;
  
  /**
   * Create a new graph
   * @param data - The graph data
   * @returns The created graph
   */
  create(data: CreateGraphDTO): Promise<Graph>;
  
  /**
   * Update an existing graph
   * @param id - The graph ID
   * @param data - The updated graph data
   * @returns The updated graph
   */
  update(id: string, data: UpdateGraphDTO): Promise<Graph>;
  
  /**
   * Delete a graph
   * @param id - The graph ID
   * @returns True if successful, false otherwise
   */
  delete(id: string): Promise<boolean>;
}
