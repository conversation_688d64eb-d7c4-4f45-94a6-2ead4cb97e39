/* eslint-disable no-console */
"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { getSupabaseClient } from "@/lib/supabase-client";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "@/types";

/**
 * Hook for working with Supabase on the client side
 * 
 * Automatically retrieves authentication token from <PERSON> and creates a Supabase client
 */
export function useSupabase() {
  const { sessionId, isLoaded, isSignedIn } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [client, setClient] = useState<SupabaseClient<Database> | null>(null);

  // Getting Supabase client
  const initializeClient = useCallback(async () => {
    if (!isLoaded || !isSignedIn) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const supabaseClient = await getSupabaseClient(sessionId);
      
      if (supabaseClient) {
        setClient(supabaseClient);
      } else {
        setError(new Error("Failed to create Supabase client"));
      }
    } catch (err) {
      console.error("[useSupabase] Error creating client:", err);
      setError(err instanceof Error ? err : new Error("Unknown error creating client"));
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, isSignedIn, sessionId]);

  // Update client when session changes
  useEffect(() => {
    if (isLoaded) {
      initializeClient().then(() => {/* do nothing */});
    }
  }, [isLoaded, sessionId, initializeClient]);

  // Refresh client
  const refreshToken = useCallback(async () => {
    return initializeClient();
  }, [initializeClient]);

  return useMemo(() => ({
    client,
    isLoading,
    error,
    refreshToken,
    isAuthenticated: isSignedIn === true,
  }), [client, isLoading, error, refreshToken, isSignedIn]);
}
