import { Node } from "@xyflow/react";

interface MiniMapNodeComponentProps {
  x: number;
  y: number;
  width: number;
  height: number;
  color?: string;
  strokeColor?: string;
  strokeWidth?: number;
  borderRadius?: number;
  node?: Node;
}

export default function MiniMapNode({
  x,
  y,
  width,
  height,
  borderRadius,
  node,
}: MiniMapNodeComponentProps) {
  // Extract label from node data if available
  const label = (node?.data?.label ?? " ") as string;

  return (
    <g>
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        rx={borderRadius || 2}
        fill="#FFFFFF" // White background to match main nodes
        stroke="#374151" // Dark border
        strokeWidth={1}
      />
      {label && (
        <text
          x={x + width / 2}
          y={y + height / 2}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize={Math.min(width, height) * 0.4}
          fill="#000000"
          fontWeight="bold"
          style={{ pointerEvents: "none" }}
        >
          {label}
        </text>
      )}
    </g>
  );
}
