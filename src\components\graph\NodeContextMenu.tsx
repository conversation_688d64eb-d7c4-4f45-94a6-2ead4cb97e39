import React, { useEffect } from 'react';
import { FiTrash2 } from 'react-icons/fi';
import { RiInformationOffFill, RiInformation2Fill } from 'react-icons/ri';
import { Node } from '@xyflow/react';

interface NodeContextMenuProps {
  x: number;
  y: number;
  nodeId: string;
  node: Node;
  onDelete: () => void;
  onToggleInclusion: () => void;
  onClose: () => void;
}

/**
 * Context menu component for nodes
 * Displays a menu with options when right-clicking on a node
 */
export const NodeContextMenu: React.FC<NodeContextMenuProps> = ({
  x,
  y,
  nodeId,
  node,
  onDelete,
  onToggleInclusion,
  onClose,
}) => {
  // <PERSON>le clicks outside the menu to close it
  useEffect(() => {
    const handleClickOutside = () => {
      onClose();
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [onClose]);

  // Calculate if menu should be positioned differently to avoid going off-screen
  const menuStyle: React.CSSProperties = {
    position: 'absolute',
    zIndex: 1000,
    // Position the menu at the cursor location
    left: x,
    top: y,
    minWidth: '180px',
  };

  // Determine if the node is included - default is true if not explicitly set
  const isIncluded = node?.data?.isIncluded === undefined ? true : node?.data?.isIncluded;
  const inclusionText = isIncluded ? 'Exclude Node' : 'Include Node';
  const inclusionClass = isIncluded ? 'text-green-400' : 'text-gray-400';

  return (
    <div 
      className="bg-gray-800 border border-gray-700 rounded-md shadow-lg"
      style={menuStyle}
      onClick={(e) => e.stopPropagation()}
      data-nodeid={nodeId}
    >
      <div className="py-1">
        <button
          className={`w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center ${inclusionClass}`}
          onClick={() => {
            onToggleInclusion();
            onClose();
          }}
        >
          {isIncluded ? <RiInformationOffFill className="mr-2 size-5" /> : <RiInformation2Fill className="mr-2 size-5" />}
          {inclusionText}
        </button>
        <div className="border-t border-gray-700 my-1"></div>
        <button
          className="w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center"
          onClick={() => {
            onDelete();
            onClose();
          }}
        >
          <FiTrash2 className="mr-2 size-5" />
          Delete Node
        </button>
      </div>
    </div>
  );
};
