import { useCallback } from "react";
import { Node } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

/**
 * Custom hook to toggle the inclusion state of a node
 * @returns Function to toggle whether a node is included or excluded
 */
export const useToggleNodeInclusion = () => {
  const {
    nodes,
    edges,
    setNodes,
    bgColor,
    selectedNodeId,
    currentNodeId,
    messages,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();

  /**
   * Toggles whether a node is included or excluded
   * @param nodeId - ID of the node to toggle inclusion state
   */
  return useCallback((nodeId: string) => {
    if (!nodeId) return;

    // Save current state to history before toggling
    saveToHistory({
      nodes: [...nodes],
      edges: [...edges],
      selectedNodeId,
      bgColor,
      currentNodeId,
      messages,
    });

    // Toggle the isIncluded property of the node
    setNodes((nds: Node[]) =>
      nds.map((node: Node) => {
        if (node.id === nodeId) {
          // If isIncluded is undefined, it means it's a new node, so default to true
          // since we're toggling from the default included state
          const currentIsIncluded = node.data?.isIncluded === undefined ? true : node.data.isIncluded;
          const newIsIncluded = !currentIsIncluded;
          
          return {
            ...node,
            data: {
              ...node.data,
              isIncluded: newIsIncluded,
            },
            // Set the class based on inclusion state
            className: newIsIncluded ? '' : 'node-excluded',
          };
        }
        return node;
      })
    );
  }, [
    nodes,
    edges,
    setNodes,
    saveToHistory,
    bgColor,
    selectedNodeId,
    currentNodeId,
    messages,
  ]);
};
