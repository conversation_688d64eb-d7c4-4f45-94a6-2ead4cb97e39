import {useCallback} from "react";
import {useGraph} from "@/contexts/GraphContext";
import {useGraphHistory} from "@/contexts/GraphHistoryContext";
import {EdgeBuilder} from "@/utils/edgeBuilder";
import {EdgeTagFilter} from "@/types/tagFilter";
import useNodeAncestors from "./useNodeAncestors";
import {NODE} from "@/lib/constants";

/**
 * Custom hook to manage edge filtering based on node tags
 */
export const useTagFilterEdge = () => {
  const {
    nodes,
    edges,
    setEdges,
    selectedNodeId,
    currentNodeId,
    bgColor,
    messages,
    setNodes,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();
  const getAncestors = useNodeAncestors();

  /**
   * Set tag filters on an edge
   * @param edgeId - The ID of the edge to filter
   * @param tagIds - Array of tag IDs to filter by (or null/empty array to remove filter)
   */
  const setEdgeTagFilter = useCallback(
    (edgeId: string, tagIds: EdgeTagFilter | null) => {
      // Normalize filter to empty array if null
      const normalizedFilter = tagIds || [];
      // Save current state to history before modifying the edge
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Update the edge with the new filter
      const updatedEdges = edges.map((edge) => {
        if (edge.id === edgeId) {
          return {
            ...edge,
            data: {
              ...edge.data,
              tagFilter: normalizedFilter,
            },
          };
        }
        return edge;
      });

      // После изменения состояния блокировки, обновляем стили узлов и ребер
      if (currentNodeId) {
        // Получаем обновленный список предков с учетом блокировки
        const ancestors = getAncestors(currentNodeId, nodes, updatedEdges);
        const ancestorIds = new Set([
          currentNodeId,
          ...ancestors.map((a) => a.id),
        ]);

        // Создаем множество ребер, входящих в путь
        const pathEdgeIds = new Set();

        // Определяем, какие ребра входят в путь
        updatedEdges.forEach((edge) => {
          if (ancestorIds.has(edge.source) && ancestorIds.has(edge.target)) {
            pathEdgeIds.add(edge.id);
          }
        });

        // Обновляем стили узлов
        setNodes((nds) =>
          nds.map((node) => {
            const isInPath = ancestorIds.has(node.id);
            return {
              ...node,
              style: {
                ...node.style,
                border: isInPath
                  ? NODE.BORDER.HIGHLIGHTED
                  : NODE.BORDER.DEFAULT,
                background: NODE.BACKGROUNDS.DEFAULT,
              },
              data: {
                ...node.data,
                isInPath,
              },
            };
          })
        );
      }
      // Update all edges with appropriate styling
      setEdges(
        updatedEdges.map((edge) => {
          // Get the current state of the edge
          const isBlocked = edge.data?.isBlocked === true;
          const isInPath = edge.data?.isInPath === true;
          const isSelected = edge.selected || false;

          // Get the tag filter for this edge
          const tagFilter = (edge.data?.tagFilter as EdgeTagFilter) || [];

          // Check if this edge has a non-empty tag filter
            // Edge is filtered if it has a filter
          const isFiltered = tagFilter.length > 0;

          // Get the appropriate edge styles based on its state
          const edgeStyles = EdgeBuilder.getEdgeStyles({
            isBlocked,
            isInPath,
            isSelected,
            isFiltered,
          });

          return {
            ...edge,
            style: edgeStyles.style,
            animated: isFiltered ? true : edgeStyles.animated,
            markerEnd: edgeStyles.markerEnd,
          };
        })
      );
    },
    [
      nodes,
      edges,
      setEdges,
      saveToHistory,
      bgColor,
      selectedNodeId,
      currentNodeId,
      messages,
      getAncestors,
      setNodes,
    ]
  );

  /**
   * Remove tag filter from an edge
   * @param edgeId - The ID of the edge to remove filter from
   */
  const removeEdgeTagFilter = useCallback(
    (edgeId: string) => {
      setEdgeTagFilter(edgeId, []);
    },
    [setEdgeTagFilter]
  );

  /**
   * Add a tag to an edge's filter
   * @param edgeId - The ID of the edge
   * @param tagId - The tag ID to add
   */
  const addTagToEdgeFilter = useCallback(
    (edgeId: string, tagId: string) => {
      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return;

      const currentFilter = (edge.data?.tagFilter as EdgeTagFilter) || [];

      // Don't add if already present
      if (currentFilter.includes(tagId)) return;

      // Add the tag to the filter
      const newFilter = [...currentFilter, tagId];
      setEdgeTagFilter(edgeId, newFilter);
    },
    [edges, setEdgeTagFilter]
  );

  /**
   * Remove a tag from an edge's filter
   * @param edgeId - The ID of the edge
   * @param tagId - The tag ID to remove
   */
  const removeTagFromEdgeFilter = useCallback(
    (edgeId: string, tagId: string) => {
      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return;

      const currentFilter = (edge.data?.tagFilter as EdgeTagFilter) || [];

      // Remove the tag from the filter
      const newFilter = currentFilter.filter((id) => id !== tagId);
      setEdgeTagFilter(edgeId, newFilter);
    },
    [edges, setEdgeTagFilter]
  );

  /**
   * Toggle a tag in an edge's filter
   * @param edgeId - The ID of the edge
   * @param tagId - The tag ID to toggle
   */
  const toggleTagInEdgeFilter = useCallback(
    (edgeId: string, tagId: string) => {
      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return;

      const currentFilter = (edge.data?.tagFilter as EdgeTagFilter) || [];

      if (currentFilter.includes(tagId)) {
        removeTagFromEdgeFilter(edgeId, tagId);
      } else {
        addTagToEdgeFilter(edgeId, tagId);
      }
    },
    [edges, addTagToEdgeFilter, removeTagFromEdgeFilter]
  );

  /**
   * Get the tag filter for an edge
   * @param edgeId - The ID of the edge
   * @returns The tag filter array or empty array if no filter
   */
  const getEdgeTagFilter = useCallback(
    (edgeId: string): EdgeTagFilter => {
      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return [];

      return (edge.data?.tagFilter as EdgeTagFilter) || [];
    },
    [edges]
  );

  return {
    setEdgeTagFilter,
    removeEdgeTagFilter,
    addTagToEdgeFilter,
    removeTagFromEdgeFilter,
    toggleTagInEdgeFilter,
    getEdgeTagFilter,
  };
};

export default useTagFilterEdge;
