# Styling Setup Documentation

## Overview
This project now uses **Tailwind CSS v4** with **shadcn/ui** components for a modern, consistent styling system.

## What Was Fixed

### 1. Tailwind CSS v4 Configuration
- ✅ Created proper `tailwind.config.ts` with v4 syntax
- ✅ Updated `postcss.config.mjs` for v4 compatibility
- ✅ Fixed CSS variable system in `globals.css`
- ✅ Added comprehensive color palette and design tokens

### 2. shadcn/ui Integration
- ✅ Properly configured `components.json`
- ✅ Added essential UI components:
  - Button (with all variants)
  - Card (header, content, description)
  - Input and Label
  - Dialog
  - Dropdown Menu
  - Badge
  - Avatar
  - Toast (already existed)

### 3. Font Configuration
- ✅ Montserrat font properly configured
- ✅ CSS variables for font family
- ✅ Fallback font system

## Current Setup

### Dependencies
```json
{
  "@tailwindcss/postcss": "^4.1.7",
  "tailwindcss": "^4.1.7",
  "autoprefixer": "^10.4.21",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "tailwind-merge": "^3.3.0"
}
```

### File Structure
```
src/
├── app/
│   ├── globals.css          # Global styles with CSS variables
│   └── test-styles/         # Test page for styling verification
├── components/ui/           # shadcn/ui components
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   ├── label.tsx
│   ├── dialog.tsx
│   ├── dropdown-menu.tsx
│   ├── badge.tsx
│   ├── avatar.tsx
│   └── toast.tsx
└── lib/
    └── utils.ts             # cn() utility function
```

### Configuration Files
- `tailwind.config.ts` - Tailwind CSS v4 configuration
- `postcss.config.mjs` - PostCSS configuration
- `components.json` - shadcn/ui configuration

## Usage Examples

### Basic Components
```tsx
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function Example() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Card</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="default">Click me</Button>
      </CardContent>
    </Card>
  );
}
```

### Color System
```tsx
// Using CSS variables
<div className="bg-primary text-primary-foreground">Primary</div>
<div className="bg-secondary text-secondary-foreground">Secondary</div>
<div className="bg-accent text-accent-foreground">Accent</div>
<div className="bg-muted text-muted-foreground">Muted</div>
```

### Responsive Design
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Responsive grid */}
</div>
```

## Testing
Visit `/test-styles` to see all components and styling in action.

## Adding New Components
To add more shadcn/ui components:
```bash
npx shadcn@latest add [component-name]
```

Popular components to consider:
- `select` - Dropdown select component
- `textarea` - Multi-line text input
- `checkbox` - Checkbox input
- `radio-group` - Radio button group
- `switch` - Toggle switch
- `tabs` - Tab navigation
- `accordion` - Collapsible content
- `alert` - Alert messages
- `progress` - Progress bars
- `skeleton` - Loading placeholders

## Dark Mode
The setup includes dark mode support via CSS variables. To enable:
1. Add theme provider to your app
2. Use the `dark` class on the root element
3. All components automatically adapt

## Best Practices
1. Use the `cn()` utility for conditional classes
2. Prefer CSS variables over hardcoded colors
3. Use semantic color names (primary, secondary, etc.)
4. Follow the component composition pattern
5. Test in both light and dark modes

## Troubleshooting
- If styles don't apply, check the Tailwind content paths in `tailwind.config.ts`
- For build issues, ensure PostCSS is properly configured
- Use the test page to verify component styling
