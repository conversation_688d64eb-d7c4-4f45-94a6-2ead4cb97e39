"use client";

import React, { useEffect, useRef } from "react";
import ChatInterface from "@/components/graph/chat/ChatInterface";
import { ClientStyleWrapper } from "@/components/providers/ClientStyleWrapper";

interface ChatPanelProps {
  graphId: string;
  width: number;
  transitionEnabled: boolean;
}

/**
 * Component for the chat panel with dynamic width
 */
export const ChatPanel: React.FC<ChatPanelProps> = ({
  graphId,
  width,
  transitionEnabled,
}) => {
  const panelRef = useRef<HTMLDivElement>(null);

  // Устанавливаем ширину панели напрямую
  useEffect(() => {
    if (panelRef.current) {
      panelRef.current.style.width = `${width}%`;
    }
  }, [width]);
  
  return (
    <ClientStyleWrapper>
      <div
        ref={panelRef}
        className={`flex flex-col bg-black min-w-0 min-h-0 overflow-hidden ${transitionEnabled ? "transition-all duration-300 ease-out" : ""}`}
      >
        {/* Chat Area */}
        <div className="flex-1 w-full h-full overflow-hidden">
          <ChatInterface graphId={graphId} />
        </div>
      </div>
    </ClientStyleWrapper>
  );
};
