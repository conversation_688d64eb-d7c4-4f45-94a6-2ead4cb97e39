import { FiPlusCircle } from "react-icons/fi";
import { ControlButton } from "./ControlButton";
import React from "react";
import { useAddButtonLogic } from "./hooks/useAddButtonLogic";

export const AddNodeButton = () => {
  const { addNode, title } = useAddButtonLogic();
  return (
    <ControlButton
      icon={<FiPlusCircle className="h-4 w-4" />}
      onClick={addNode}
      title={title}
      className="bg-blue-600 hover:bg-blue-700 !bg-opacity-100"
    />
  );
};