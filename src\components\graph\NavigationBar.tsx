import React from "react";
import { HistoryControls } from "@/components/graph/controls/HistoryControls";
import { ZoomControls } from "@/components/graph/controls/ZoomControls";
import { GraphOperationControls } from "@/components/graph/controls/GraphOperationControls";
import { Menu } from "@/components/graph/Menu";
import { useGraph } from "@/contexts/GraphContext";
import { AIProvidersControls } from "./controls/AIProvidersControls";
import { VerticalDivider } from "@/components/ui/VerticalDivider";

interface NavigationBarProps {
  graphId?: string;
}

/**
 * Component for the navigation bar containing all graph controls
 */
export const NavigationBar: React.FC<NavigationBarProps> = ({ graphId }) => {
  const { graphTitle } = useGraph();

  return (
    <nav className="h-[40px] bg-gray-900 flex items-center justify-between px-4 border-b border-gray-800">
      <div className="h-8 w-full flex items-center justify-start">
        {/* Menu */}
        <Menu graphId={graphId} title={graphTitle || "New Graph"} />
        {/* Vertical Divider */}
        <VerticalDivider margin="mx-2"/> 
        {/* Part with controls */}
        <div className="flex items-center">
          {/* History Controls */}
          <HistoryControls />

          {/* Vertical Divider */}
          <VerticalDivider margin="mr-2"/> 

          {/* Zoom Controls */}
          <ZoomControls />

          {/* Vertical Divider */}
          <div className="h-8 w-px bg-gray-700 mr-2"></div>

          {/* Graph Operation Controls */}
          <GraphOperationControls />
        </div>
      </div>
      {/* AI Provider Controls */}
      <AIProvidersControls />
    </nav>
  );
};
