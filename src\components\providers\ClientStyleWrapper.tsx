"use client";

import React, { ReactNode } from 'react';

interface ClientStyleWrapperProps {
  children: ReactNode;
}

/**
 * Компонент-обертка для клиентского рендеринга стилизованных элементов
 * Этот компонент помогает избежать проблем гидратации с атрибутом style
 * при использовании SSR
 */
export function ClientStyleWrapper({ children }: ClientStyleWrapperProps): JSX.Element {
  return <>{children}</>;
}
