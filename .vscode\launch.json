{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "openExternally"}}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:4000"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npm run dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"type": "node", "request": "attach", "name": "Launch Program", "skipFiles": ["<node_internals>/**"], "port": 9229}]}