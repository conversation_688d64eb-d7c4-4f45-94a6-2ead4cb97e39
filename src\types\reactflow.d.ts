declare module "reactflow" {
  import * as React from "react";

  // Node types
  export interface Node<T = Record<string, unknown>> {
    id: string;
    position: {
      x: number;
      y: number;
    };
    data: T;
    type?: string;
    style?: React.CSSProperties;
    className?: string;
    targetPosition?: string;
    sourcePosition?: string;
    hidden?: boolean;
    selected?: boolean;
    dragging?: boolean;
    draggable?: boolean;
    selectable?: boolean;
    connectable?: boolean;
    dragHandle?: string;
    width?: number;
    height?: number;
    parentNode?: string;
    zIndex?: number;
    extent?: "parent" | [number, number, number, number];
    expandParent?: boolean;
    positionAbsolute?: {
      x: number;
      y: number;
    };
    ariaLabel?: string;
    focusable?: boolean;
    resizing?: boolean;
  }

  // Edge types
  export interface Edge<T = Record<string, unknown>> {
    id: string;
    source: string;
    target: string;
    sourceHandle?: string | null;
    targetHandle?: string | null;
    type?: string;
    animated?: boolean;
    hidden?: boolean;
    deletable?: boolean;
    data?: T;
    style?: React.CSSProperties;
    className?: string;
    selected?: boolean;
    markerEnd?: {
      type: MarkerType;
      color?: string;
      width?: number;
      height?: number;
      strokeWidth?: number;
    };
    markerStart?: {
      type: MarkerType;
      color?: string;
      width?: number;
      height?: number;
      strokeWidth?: number;
    };
    zIndex?: number;
    ariaLabel?: string;
    interactionWidth?: number;
    focusable?: boolean;
  }

  // Marker types
  export enum MarkerType {
    Arrow = "arrow",
    ArrowClosed = "arrowclosed",
  }

  // Change types
  export type NodeChange =
    | {
        type: "add" | "remove";
        item: Node;
      }
    | {
        type: "position";
        id: string;
        position?: {
          x: number;
          y: number;
        };
        positionAbsolute?: {
          x: number;
          y: number;
        };
        dragging?: boolean;
      }
    | {
        type: "dimensions";
        id: string;
        dimensions?: {
          width: number;
          height: number;
        };
      }
    | {
        type: "select";
        id: string;
        selected: boolean;
      };

  export type EdgeChange =
    | {
        type: "add" | "remove";
        item: Edge;
      }
    | {
        type: "select";
        id: string;
        selected: boolean;
      };

  // Connection types
  export interface Connection {
    source: string | null;
    target: string | null;
    sourceHandle: string | null;
    targetHandle: string | null;
  }

  // ReactFlow instance type
  export interface ReactFlowInstance {
    getNodes: () => Node[];
    getEdges: () => Edge[];
    setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
    setEdges: (edges: Edge[] | ((edges: Edge[]) => Edge[])) => void;
    addNodes: (nodes: Node[]) => void;
    addEdges: (edges: Edge[]) => void;
    toObject: () => {
      nodes: Node[];
      edges: Edge[];
      viewport: { x: number; y: number; zoom: number };
    };
    getViewport: () => { x: number; y: number; zoom: number };
    setViewport: (viewport: { x: number; y: number; zoom: number }) => void;
    fitView: (options?: {
      padding?: number;
      includeHiddenNodes?: boolean;
      minZoom?: number;
      maxZoom?: number;
    }) => void;
    zoomIn: () => void;
    zoomOut: () => void;
    zoomTo: (zoomLevel: number) => void;
    getNode: (nodeId: string) => Node | undefined;
    getEdge: (edgeId: string) => Edge | undefined;
    deleteElements: (params: { nodes?: Node[]; edges?: Edge[] }) => void;
    project: (position: { x: number; y: number }) => { x: number; y: number };
    screenToFlowPosition: (position: { x: number; y: number }) => {
      x: number;
      y: number;
    };
  }

  // Component props
  export interface ReactFlowProps<
    NodeDataType = Record<string, unknown>,
    EdgeDataType = Record<string, unknown>,
  > {
    nodes: Node<NodeDataType>[];
    edges: Edge<EdgeDataType>[];
    defaultNodes?: Node<NodeDataType>[];
    defaultEdges?: Edge<EdgeDataType>[];
    onNodesChange?: (changes: NodeChange[]) => void;
    onEdgesChange?: (changes: EdgeChange[]) => void;
    onNodeClick?: (event: React.MouseEvent, node: Node<NodeDataType>) => void;
    onNodeDoubleClick?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeMouseEnter?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeMouseMove?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeMouseLeave?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeContextMenu?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeDragStart?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onNodeDrag?: (event: React.MouseEvent, node: Node<NodeDataType>) => void;
    onNodeDragStop?: (
      event: React.MouseEvent,
      node: Node<NodeDataType>,
    ) => void;
    onEdgeClick?: (event: React.MouseEvent, edge: Edge<EdgeDataType>) => void;
    onEdgeDoubleClick?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeMouseEnter?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeMouseMove?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeMouseLeave?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeContextMenu?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeUpdate?: (
      oldEdge: Edge<EdgeDataType>,
      newConnection: Connection,
    ) => void;
    onEdgeUpdateStart?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onEdgeUpdateEnd?: (
      event: React.MouseEvent,
      edge: Edge<EdgeDataType>,
    ) => void;
    onConnect?: (connection: Connection) => void;
    onConnectStart?: (
      event: React.MouseEvent,
      params: { nodeId: string; handleId: string | null },
    ) => void;
    onConnectEnd?: (event: React.MouseEvent) => void;
    onClickConnectStart?: (
      event: React.MouseEvent,
      params: { nodeId: string; handleId: string | null },
    ) => void;
    onClickConnectEnd?: (event: React.MouseEvent) => void;
    onInit?: (reactFlowInstance: ReactFlowInstance) => void;
    onMove?: (
      event: React.MouseEvent,
      viewport: { x: number; y: number; zoom: number },
    ) => void;
    onMoveStart?: (
      event: React.MouseEvent,
      viewport: { x: number; y: number; zoom: number },
    ) => void;
    onMoveEnd?: (
      event: React.MouseEvent,
      viewport: { x: number; y: number; zoom: number },
    ) => void;
    onSelectionChange?: (elements: {
      nodes: Node<NodeDataType>[];
      edges: Edge<EdgeDataType>[];
    }) => void;
    onSelectionDragStart?: (
      event: React.MouseEvent,
      nodes: Node<NodeDataType>[],
    ) => void;
    onSelectionDrag?: (
      event: React.MouseEvent,
      nodes: Node<NodeDataType>[],
    ) => void;
    onSelectionDragStop?: (
      event: React.MouseEvent,
      nodes: Node<NodeDataType>[],
    ) => void;
    onSelectionContextMenu?: (
      event: React.MouseEvent,
      nodes: Node<NodeDataType>[],
    ) => void;
    onPaneClick?: (event: React.MouseEvent) => void;
    onPaneContextMenu?: (event: React.MouseEvent) => void;
    onPaneScroll?: (event: React.MouseEvent) => void;
    onPaneMouseEnter?: (event: React.MouseEvent) => void;
    onPaneMouseMove?: (event: React.MouseEvent) => void;
    onPaneMouseLeave?: (event: React.MouseEvent) => void;
    children?: React.ReactNode;
    elementsSelectable?: boolean;
    nodesDraggable?: boolean;
    nodesConnectable?: boolean;
    nodesFocusable?: boolean;
    edgesFocusable?: boolean;
    minZoom?: number;
    maxZoom?: number;
    defaultViewport?: { x: number; y: number; zoom: number };
    translateExtent?: [[number, number], [number, number]];
    preventScrolling?: boolean;
    nodeExtent?: [[number, number], [number, number]];
    defaultMarkerColor?: string;
    zoomOnScroll?: boolean;
    zoomOnPinch?: boolean;
    panOnScroll?: boolean;
    panOnScrollMode?: "free" | "horizontal" | "vertical";
    panOnScrollSpeed?: number;
    panOnDrag?: boolean;
    onlyRenderVisibleElements?: boolean;
    defaultEdgeOptions?: {
      type?: string;
      animated?: boolean;
      hidden?: boolean;
      deletable?: boolean;
      style?: React.CSSProperties;
      className?: string;
    };
    fitView?: boolean;
    fitViewOptions?: {
      padding?: number;
      includeHiddenNodes?: boolean;
      minZoom?: number;
      maxZoom?: number;
    };
    connectOnClick?: boolean;
    attributionPosition?:
      | "top-right"
      | "top-left"
      | "bottom-right"
      | "bottom-left";
    proOptions?: { hideAttribution?: boolean };
    elevateNodesOnSelect?: boolean;
    elevateEdgesOnSelect?: boolean;
    disableKeyboardA11y?: boolean;
    autoPanOnConnect?: boolean;
    autoPanOnNodeDrag?: boolean;
    connectionRadius?: number;
    onError?: (error: Error) => void;
  }

  // Background component props
  export interface BackgroundProps {
    variant?: "dots" | "lines" | "cross";
    gap?: number;
    size?: number;
    color?: string;
    style?: React.CSSProperties;
    className?: string;
    id?: string;
  }

  // MiniMap component props
  export interface MiniMapProps {
    nodeStrokeColor?: string;
    nodeColor?: string;
    nodeBorderRadius?: number;
    nodeClassName?: string;
    nodeStrokeWidth?: number;
    maskColor?: string;
    maskStrokeColor?: string;
    maskStrokeWidth?: number;
    position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
    style?: React.CSSProperties;
    className?: string;
    pannable?: boolean;
    zoomable?: boolean;
    ariaLabel?: string;
  }

  // Controls component props
  export interface ControlsProps {
    showZoom?: boolean;
    showFitView?: boolean;
    showInteractive?: boolean;
    fitViewOptions?: {
      padding?: number;
      includeHiddenNodes?: boolean;
      minZoom?: number;
      maxZoom?: number;
    };
    onZoomIn?: () => void;
    onZoomOut?: () => void;
    onFitView?: () => void;
    onInteractiveChange?: (interactiveStatus: boolean) => void;
    position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
    style?: React.CSSProperties;
    className?: string;
  }

  // Component exports
  export const ReactFlow: React.FC<ReactFlowProps>;
  export const Background: React.FC<BackgroundProps>;
  export const MiniMap: React.FC<MiniMapProps>;
  export const Controls: React.FC<ControlsProps>;
  export const Panel: React.FC<{
    position?:
      | "top-left"
      | "top-center"
      | "top-right"
      | "bottom-left"
      | "bottom-center"
      | "bottom-right";
    className?: string;
    style?: React.CSSProperties;
    children?: React.ReactNode;
  }>;

  // Utility functions
  export function applyNodeChanges<T = Record<string, unknown>>(
    changes: NodeChange[],
    nodes: Node<T>[],
  ): Node<T>[];
  export function applyEdgeChanges<T = Record<string, unknown>>(
    changes: EdgeChange[],
    edges: Edge<T>[],
  ): Edge<T>[];
  export function addEdge<T = Record<string, unknown>>(
    edge: Edge<T> | Connection,
    edges: Edge<T>[],
  ): Edge<T>[];

  export default ReactFlow;
}
