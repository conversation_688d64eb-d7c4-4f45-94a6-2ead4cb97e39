import { useCallback } from "react";
import { Edge } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { EdgeBuilder } from "@/utils/edgeBuilder";
import { EdgeTagFilter } from "@/types/tagFilter";
import { NodeTag } from "@/utils/nodeBuilder";

/**
 * Custom hook to handle edge selection functionality
 * Provides functions to select and deselect edges
 */
export const useSelectEdge = () => {
  const { edges, setEdges, nodes } = useGraph();

  /**
   * Проверяет, проходит ли узел фильтр тегов
   * @param nodeId - ID узла для проверки
   * @param tagFilter - Массив тегов для фильтрации
   * @returns True если узел проходит фильтр
   */
  const nodePassesFilter = useCallback((nodeId: string, tagFilter: EdgeTagFilter): boolean => {
    // Если фильтр пуст, все узлы проходят
    if (!tagFilter || tagFilter.length === 0) {
      return true;
    }
    
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return false;
    
    const nodeTags = node.data?.tags as NodeTag[] || [];
    
    // Узел проходит, если имеет хотя бы один из тегов в фильтре (логика ИЛИ)
    return nodeTags.some(tag => tagFilter.includes(tag.id));
  }, [nodes]);

  /**
   * Select an edge by its ID
   * @param edgeId - The ID of the edge to select
   */
  const selectEdge = useCallback(
    (edgeId: string) => {
      if (!edgeId) return;

      // Update all edges, setting the selected one to have selected: true
      setEdges((eds: Edge[]) =>
        eds.map((edge) => {
          // Check if this edge is part of an active path
          const isInPath = !!edge.data?.isInPath;
          // Check if this edge is blocked
          const isBlocked = !!edge.data?.isBlocked;
          
          // Получаем фильтр тегов для этого ребра
          const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
          
          // Проверяем, проходит ли целевой узел фильтр
          const hasTagFilter = tagFilter.length > 0;
          const targetNodeId = edge.target;
          const targetNodePasses = nodePassesFilter(targetNodeId, tagFilter);
          
          // Ребро фильтруется, если имеет фильтр и целевой узел не проходит
          const isFiltered = hasTagFilter && !targetNodePasses;
          
          if (edge.id === edgeId) {
            // This is the edge we're selecting
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath,
              isSelected: true,
              isFiltered
            });
            
            return {
              ...edge,
              selected: true,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              markerEnd: edgeStyles.markerEnd,
            };
          } else {
            // For other edges, just make sure they're not selected
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked,
              isInPath,
              isSelected: false,
              isFiltered
            });
            
            return {
              ...edge,
              selected: false,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              markerEnd: edgeStyles.markerEnd,
            };
          }
        })
      );
    },
    [setEdges, nodePassesFilter]
  );

  /**
   * Deselect all edges
   */
  const deselectEdges = useCallback(() => {
    setEdges((eds: Edge[]) =>
      eds.map((edge) => {
        // Check if this edge is part of an active path
        const isInPath = edge.data?.isInPath === true;
        // Check if this edge is blocked
        const isBlocked = edge.data?.isBlocked === true;
        
        // Получаем фильтр тегов для этого ребра
        const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
        
        // Проверяем, проходит ли целевой узел фильтр
        const hasTagFilter = tagFilter.length > 0;
        const targetNodeId = edge.target;
        const targetNodePasses = nodePassesFilter(targetNodeId, tagFilter);
        
        // Ребро фильтруется, если имеет фильтр и целевой узел не проходит
        const isFiltered = hasTagFilter && !targetNodePasses;
        
        const edgeStyles = EdgeBuilder.getEdgeStyles({
          isBlocked,
          isInPath,
          isSelected: false,
          isFiltered
        });
        
        return {
          ...edge,
          selected: false,
          style: edgeStyles.style,
          animated: edgeStyles.animated,
          markerEnd: edgeStyles.markerEnd,
        };
      })
    );
  }, [setEdges, nodePassesFilter]);

  /**
   * Toggle selection of an edge
   * @param edgeId - The ID of the edge to toggle selection
   */
  const toggleEdgeSelection = useCallback(
    (edgeId: string) => {
      if (!edgeId) return;

      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return;

      if (edge.selected) {
        deselectEdges();
      } else {
        selectEdge(edgeId);
      }
    },
    [edges, selectEdge, deselectEdges]
  );

  /**
   * Handle edge click event
   * @param event - The mouse event
   * @param edge - The edge that was clicked
   */
  const onEdgeClick = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.stopPropagation(); // Prevent triggering other click handlers
      toggleEdgeSelection(edge.id);
    },
    [toggleEdgeSelection]
  );

  return {
    selectEdge,
    deselectEdges,
    toggleEdgeSelection,
    onEdgeClick,
  };
};

export default useSelectEdge;
