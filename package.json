{"name": "graph", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start", "lint": "eslint src/**/*.ts{,x}", "lint:fix": "eslint src/**/*.ts{,x} --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css}\"", "debug": "cross-env NODE_OPTIONS=--inspect=9229 next dev -p 4000", "check": "tsc --noEmit && eslint src/**/*.ts{,x}"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/react": "^1.2.12", "@chatscope/chat-ui-kit-react": "^2.1.1", "@clerk/nextjs": "^6.20.0", "@clerk/themes": "^2.2.46", "@heroicons/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.77.2", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.6.4", "ai": "^4.3.16", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.12.4", "katex": "^0.16.22", "lucide-react": "^0.511.0", "nanoid": "^5.1.5", "next": "^15.3.2", "next-themes": "^0.4.6", "openai": "^4.103.0", "prismjs": "^1.30.0", "react": "^19", "react-color": "^2.19.3", "react-dom": "^19", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "svix": "^1.66.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.28"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "6.0.0-rc.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}}