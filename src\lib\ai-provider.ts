import { openai as aiSdkOpenAI } from "@ai-sdk/openai";
import { anthropic as aiSdkAnthropic } from "@ai-sdk/anthropic";
import { google as aiSdkGoogle } from "@ai-sdk/google";
import { perplexity as aiSdkPerplexity } from "@ai-sdk/perplexity";
import { OpenAIModel, AnthropicModel, GoogleModel, PerplexityModel, Providers } from "./constants";

export const aiProviders = {
  [Providers.OPENAI]: {aiSdk: aiSdkOpenAI, models: OpenAIModel },
  [Providers.ANTHROPIC]: {aiSdk: aiSdkAnthropic, models: AnthropicModel},
  [Providers.GOOGLE]: {aiSdk: aiSdkGoogle, models: GoogleModel},
  [Providers.PERPLEXITY]: {aiSdk: aiSdkPerplexity, models: PerplexityModel},
} as const;
