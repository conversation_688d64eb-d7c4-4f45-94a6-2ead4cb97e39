---
description: 
globs: 
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

# CSS

- Use Tailwind CSS for styling.
- Use Tailwind's arbitrary values for dynamic values.
- Do not change CSS styles unless the prompt explicitly states that they need to be changed.

# Hooks

- When returning value from custom hook is Object, always use useMemo to memoize the value.
example: return useMemo(() => ({ getParentNodes, getChildNodes }), [someDependency]);

# General Code Style & Formatting

- Follow the Airbnb Style Guide for code formatting.
- Use English for all code and documentation.
- Always declare the type of each variable and function (parameters and return value).
- Avoid using any.
- Create necessary types.