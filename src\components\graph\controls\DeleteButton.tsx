import { FiTrash2 } from "react-icons/fi";
import { ControlButton } from "./ControlButton";
import React from "react";
import { useDeleteButtonLogic } from "./hooks/useDeleteButtonLogic";

export const DeleteButton = () => {
  const { deleteSelectedItem, hasSelectedItem, title } = useDeleteButtonLogic();
  return (
    <ControlButton
      icon={<FiTrash2 className="h-4 w-4" />}
      onClick={deleteSelectedItem}
      disabled={!hasSelectedItem}
      title={title}
      className={
        hasSelectedItem ? "bg-red-500 hover:bg-red-600 !bg-opacity-100" : ""
      }
    />
  );
};
