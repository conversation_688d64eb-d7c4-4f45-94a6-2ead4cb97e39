/* eslint-disable no-console */
// noinspection ExceptionCaughtLocallyJS

import { getSupabaseClient, getSupabaseAdmin } from "@/lib/supabase-client";
import { IGraphRepository } from "@/repositories";
import { GraphDTO, CreateGraphDTO, UpdateGraphDTO } from "@/types/dto";

/**
 * Supabase implementation of the Graph repository
 */
export class SupabaseGraphRepository implements IGraphRepository {
  private supabasePromise;
  private sessionId?: string;
  private fallbackToAdmin: boolean = false;
  private isServerSide: boolean;

  constructor(sessionId?: string) {
    this.sessionId = sessionId;
    // Determine if code is running on the server
    // Determine if code is running on the server
    this.isServerSide = typeof window === 'undefined';
    // If code is running on the server, use admin access immediately
    // If code is running on the server, use admin access immediately
    if (this.isServerSide) {
      this.fallbackToAdmin = true;
    }
    this.supabasePromise = this.getClient();
  }

  /**
   * Get Supabase client with error handling
   * Get Supabase client with error handling
   */
  private getClient() {
    if (this.fallbackToAdmin) {
      return Promise.resolve(getSupabaseAdmin());
    }

    try {
      return getSupabaseClient(this.sessionId);
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Error getting client, switching to admin access:`, error);
      console.error(`[SupabaseGraphRepository] Error getting client, switching to admin access:`, error);
      this.fallbackToAdmin = true;
      return Promise.resolve(getSupabaseAdmin());
    }
  }

  /**
   * Get client with possible reload in case of error
   * Get client with possible reload in case of error
   */
  private async getSafeClient() {
    try {
      return await this.supabasePromise;
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Error getting client, trying to reload`, error);
      console.error(`[SupabaseGraphRepository] Error getting client, trying to reload`, error);
      this.supabasePromise = this.getClient();
      return this.supabasePromise;
    }
  }

  /**
   * Find a graph by ID
   */
  async findById(id: string): Promise<GraphDTO | null> {
    try {
      const supabase = await this.getSafeClient();
      const { data, error } = await supabase
        .from('chat_graphs')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Record not found
          // Record not found
          return null;
        }
        
        // If error is related to JWT, try using admin access
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = this.getClient();
            return this.findById(id);
          }
        }
        
        console.error(`[SupabaseGraphRepository] Error finding graph by id:`, error);
        return null;
      }

      if (!data) return null;

      return this.mapToGraphDTO(data);
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Unexpected error in findById:`, error);
      return null;
    }
  }

  /**
   * Find all graphs for a user
   */
  async findByUserId(userId: string): Promise<GraphDTO[]> {
    try {
      const supabase = await this.getSafeClient();
      const { data, error } = await supabase
        .from('chat_graphs')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        // If error is related to JWT, try using admin access
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = this.getClient();
            return this.findByUserId(userId);
          }
        }
        
        console.error(`[SupabaseGraphRepository] Error finding graphs by user id:`, error);
        return [];
      }

      if (!data) return [];

      return data.map(this.mapToGraphDTO);
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Unexpected error in findByUserId:`, error);
      return [];
    }
  }

  /**
   * Create a new graph
   */
  async create(data: CreateGraphDTO): Promise<GraphDTO> {
    try {
      const supabase = await this.getSafeClient();
      const graphData = {
        title: data.title,
        description: data.description || null,
        user_id: data.userId,
        bg_color: data.bgColor || '#ffffff',
        settings: data.settings || {}
      };

      const { data: newGraph, error } = await supabase
        .from('chat_graphs')
        .insert(graphData)
        .select()
        .single();

      if (error) {
        // If error is related to JWT, try using admin access
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = this.getClient();
            return this.create(data);
          }
        }
        
        throw new Error(`Failed to create graph: ${error.message}`);
      }

      if (!newGraph) {
        throw new Error('Failed to create graph: No data returned');
      }

      return this.mapToGraphDTO(newGraph);
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Error creating graph:`, error);
      throw error;
    }
  }

  /**
   * Update an existing graph
   */
  async update(id: string, data: UpdateGraphDTO): Promise<GraphDTO> {
    try {
      const supabase = await this.getSafeClient();
      
      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString(),
      };

      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.bgColor !== undefined) updateData.bg_color = data.bgColor;
      if (data.settings !== undefined) updateData.settings = data.settings;

      const { data: updatedGraph, error } = await supabase
        .from('chat_graphs')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`[SupabaseGraphRepository] Database update error:`, error.message);
        
        // If error is related to JWT, try using admin access
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = this.getClient();
            return this.update(id, data);
          }
        }
        
        throw new Error(`Failed to update graph: ${error.message}`);
      }

      if (!updatedGraph) {
        console.error(`[SupabaseGraphRepository] No data returned from update operation`);
        throw new Error('Failed to update graph: No data returned');
      }
      const mappedGraph = this.mapToGraphDTO(updatedGraph);

      
      return mappedGraph;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[SupabaseGraphRepository] Error updating graph:`, errorMessage);
      throw error;
    }
  }

  /**
   * Delete a graph
   */
  async delete(id: string): Promise<boolean> {
    try {
      const supabase = await this.getSafeClient();
      const { error } = await supabase
        .from('chat_graphs')
        .delete()
        .eq('id', id);

      if (error) {
        // If error is related to JWT, try using admin access
        // If error is related to JWT, try using admin access
        if (error.message && (error.message.includes('JWT') || error.message.includes('token') || error.message.includes('auth'))) {
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          console.error(`[SupabaseGraphRepository] JWT error detected, switching to admin client`);
          
          if (!this.fallbackToAdmin) {
            this.fallbackToAdmin = true;
            this.supabasePromise = this.getClient();
            return this.delete(id);
          }
        }
        
        throw new Error(`Failed to delete graph: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error(`[SupabaseGraphRepository] Error deleting graph:`, error);
      return false;
    }
  }

  /**
   * Map database record to GraphDTO
   */
  private mapToGraphDTO(data: Record<string, unknown>): GraphDTO {
    return {
      id: data.id as string,
      title: data.title as string,
      description: data.description as string,
      userId: data.user_id as string,
      bgColor: data.bg_color as string,
      settings: data.settings as Record<string, unknown> || {},
      createdAt: new Date(data.created_at as string),
      updatedAt: new Date(data.updated_at as string)
    };
  }
}
