import { NodeMetadata, CreateNodeMetadataDTO, UpdateNodeMetadataDTO } from "@/types/dto";

/**
 * Interface for Node Metadata repository
 * Defines methods for interacting with node metadata
 */
export interface INodeMetadataRepository {
  /**
   * Find metadata for a specific node
   * @param nodeId - The node ID
   * @returns The node metadata or null if not found
   */
  findByNodeId(nodeId: string): Promise<NodeMetadata | null>;
  
  /**
   * Create metadata for a node
   * @param data - The metadata data
   * @returns The created metadata
   */
  create(data: CreateNodeMetadataDTO): Promise<NodeMetadata>;
  
  /**
   * Update metadata for a node
   * @param nodeId - The node ID
   * @param data - The updated metadata
   * @returns The updated metadata
   */
  update(nodeId: string, data: UpdateNodeMetadataDTO): Promise<NodeMetadata>;
  
  /**
   * Delete metadata for a node
   * @param nodeId - The node ID
   * @returns True if successful, false otherwise
   */
  delete(nodeId: string): Promise<boolean>;
  
  /**
   * Add a tag to a node
   * @param nodeId - The node ID
   * @param tag - The tag to add
   * @returns The updated metadata
   */
  addTag(nodeId: string, tag: string): Promise<NodeMetadata>;
  
  /**
   * Remove a tag from a node
   * @param nodeId - The node ID
   * @param tag - The tag to remove
   * @returns The updated metadata
   */
  removeTag(nodeId: string, tag: string): Promise<NodeMetadata>;
  
  /**
   * Update the embedding vector for a node
   * @param nodeId - The node ID
   * @param embedding - The embedding vector
   * @returns The updated metadata
   */
  updateEmbedding(nodeId: string, embedding: number[]): Promise<NodeMetadata>;
}
