/**
 * Types for tag-based edge filtering
 */

/**
 * Enum for filter operators
 */
export enum FilterOperator {
  TAG = 'TAG',
  NOT = 'NOT',
  AND = 'AND',
  OR = 'OR',
}

/**
 * Base interface for all tag filters
 */
export interface BaseTagFilter {
  type: FilterOperator;
}

/**
 * Filter that checks for the presence of a specific tag
 */
export interface TagPresenceFilter extends BaseTagFilter {
  type: FilterOperator.TAG;
  tagId: string;
}

/**
 * Filter that negates another filter
 */
export interface NotTagFilter extends BaseTagFilter {
  type: FilterOperator.NOT;
  filter: TagFilter;
}

/**
 * Filter that requires all child filters to be true (AND)
 */
export interface AndTagFilter extends BaseTagFilter {
  type: FilterOperator.AND;
  filters: TagFilter[];
}

/**
 * Filter that requires at least one child filter to be true (OR)
 */
export interface OrTagFilter extends BaseTagFilter {
  type: FilterOperator.OR;
  filters: TagFilter[];
}

/**
 * Union type for all possible tag filters
 */
export type TagFilter = 
  | TagPresenceFilter
  | NotTagFilter
  | AndTagFilter
  | OrTagFilter;

/**
 * Simple tag filter array for edge filtering
 * Empty array means no filtering
 * Non-empty array means only nodes with at least one of these tags are allowed (OR logic)
 */
export type EdgeTagFilter = string[];

/**
 * Utility class for creating tag filters
 */
export class TagFilters {
  /**
   * Create a filter that checks for the presence of a specific tag
   * @param tagId - The ID of the tag to check for
   * @returns A tag presence filter
   */
  public static hasTag(tagId: string): TagPresenceFilter {
    return {
      type: FilterOperator.TAG,
      tagId,
    };
  }

  /**
   * Create a filter that negates another filter
   * @param filter - The filter to negate
   * @returns A NOT filter
   */
  public static not(filter: TagFilter): NotTagFilter {
    return {
      type: FilterOperator.NOT,
      filter,
    };
  }

  /**
   * Create a filter that requires all child filters to be true (AND)
   * @param filters - The filters to combine with AND
   * @returns An AND filter
   */
  public static and(filters: TagFilter[]): AndTagFilter {
    return {
      type: FilterOperator.AND,
      filters,
    };
  }

  /**
   * Create a filter that requires at least one child filter to be true (OR)
   * @param filters - The filters to combine with OR
   * @returns An OR filter
   */
  public static or(filters: TagFilter[]): OrTagFilter {
    return {
      type: FilterOperator.OR,
      filters,
    };
  }
}
