/**
 * Data Transfer Objects (DTOs) for the application
 * These types define the shape of data transferred between layers
 */

// User DTOs
export interface UserDTO {
  id: string;
  email: string;
  clerkId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDTO {
  id?: string;
  email: string;
  clerkId?: string;
}

export interface UpdateUserDTO {
  email?: string;
  clerkId?: string;
}

// Graph DTOs
export interface GraphDTO {
  id: string;
  title: string;
  description?: string | null;
  bgColor?: string;           // Background color of the graph
  settings?: GraphSettings;   // Additional graph settings
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface GraphSettings {
  snapToGrid?: boolean;       // Whether nodes should snap to grid
  gridSize?: number;          // Size of the grid
  zoomLevel?: number;         // Default zoom level
  panOffset?: { x: number; y: number }; // Default pan offset
  nodeTypes?: string[];       // Allowed node types
  edgeTypes?: string[];       // Allowed edge types
  sortByDepth?: boolean;      // Whether to sort messages by depth instead of time
}

export interface CreateGraphDTO {
  title: string;
  description?: string;
  bgColor?: string;
  settings?: GraphSettings;
  userId: string;
}

export interface UpdateGraphDTO {
  title?: string;
  description?: string;
  bgColor?: string;
  settings?: GraphSettings;
}

// Node DTOs
export interface NodeDTO {
  id: string;
  label: string;
  x: number;
  y: number;
  type: string;
  userDialog?: string;        // Explicit field for user dialog
  assistantDialog?: string;   // Explicit field for assistant dialog
  tags?: string[];            // Explicit field for node tags
  isIncluded?: boolean;       // Track inclusion state
  data?: Record<string, any>; // For other custom data
  createdAt: Date;
  updatedAt: Date;
  graphId: string;
}

export interface CreateNodeDTO {
  label: string;
  x: number;
  y: number;
  type?: string;
  userDialog?: string;
  assistantDialog?: string;
  tags?: string[];
  isIncluded?: boolean;
  data?: Record<string, any>;
  graphId: string;
}

export interface UpdateNodeDTO {
  label?: string;
  x?: number;
  y?: number;
  type?: string;
  userDialog?: string;
  assistantDialog?: string;
  tags?: string[];
  isIncluded?: boolean;
  data?: Record<string, any>;
}

// Edge DTOs
export interface EdgeDTO {
  id: string;
  label?: string | null;
  type: string;
  sourceId: string;
  targetId: string;
  isBlocked?: boolean;        // Explicit field for blocked state
  tagFilter?: string[];       // Explicit field for tag filters
  data?: Record<string, any>; // For other custom data
  createdAt: Date;
  updatedAt: Date;
  graphId: string;
}

export interface CreateEdgeDTO {
  label?: string;
  type?: string;
  sourceId: string;
  targetId: string;
  isBlocked?: boolean;
  tagFilter?: string[];
  data?: Record<string, any>;
  graphId: string;
}

export interface UpdateEdgeDTO {
  label?: string;
  type?: string;
  sourceId?: string;
  targetId?: string;
  isBlocked?: boolean;
  tagFilter?: string[];
  data?: Record<string, any>;
}

// Tag DTOs
export interface TagDTO {
  id: string;
  label: string;
  icon?: string;
  color?: string;
  shortLabel?: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: string; // If tags can be user-specific
}

export interface CreateTagDTO {
  label: string;
  icon?: string;
  color?: string;
  shortLabel?: string;
  userId?: string;
}

export interface UpdateTagDTO {
  label?: string;
  icon?: string;
  color?: string;
  shortLabel?: string;
}

// Node Metadata DTOs
export interface NodeMetadata {
  id: string;
  nodeId: string;
  tags: string[];
  embedding?: number[];
  additionalData?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNodeMetadataDTO {
  nodeId: string;
  tags?: string[];
  embedding?: number[];
  additionalData?: Record<string, unknown>;
}

export interface UpdateNodeMetadataDTO {
  tags?: string[];
  embedding?: number[];
  additionalData?: Record<string, unknown>;
}
