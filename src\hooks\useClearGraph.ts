import { useCallback } from "react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

/**
 * Custom hook to handle clearing the graph
 * Clears all nodes and edges while maintaining the current background color
 * @returns Function to clear the graph
 */
export const useClearGraph = () => {
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    setMessages,
    currentNodeId,
    messages,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();

  /**
   * Clears the graph
   */
  return useCallback(() => {
    // Save current state to history before clearing
    saveToHistory({
      nodes: [...nodes],
      edges: [...edges],
      selectedNodeId,
      bgColor,
      currentNodeId,
      messages,
    });

    // Clear nodes and edges
    setNodes([]);
    setEdges([]);
    setMessages([]);

    // Reset selected node and current node
    setSelectedNodeId(null);
    setCurrentNodeId(null);
  }, [
    nodes,
    edges,
    setNodes,
    setEdges,
    saveToHistory,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    setMessages,
    currentNodeId,
    messages,
  ]);
};
