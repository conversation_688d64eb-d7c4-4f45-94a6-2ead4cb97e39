import { useState, useCallback, useRef, MouseEvent } from 'react';
import { Node } from '@xyflow/react';
import { useDeleteNode } from './useDeleteNode';
import { useToggleNodeInclusion } from './useToggleNodeInclusion';
import { useGraph } from '@/contexts/GraphContext';

interface ContextMenuState {
  x: number;
  y: number;
  nodeId: string;
  node: Node | null;
  show: boolean;
}

/**
 * Custom hook to manage node context menu
 * Provides state and handlers for the node context menu
 */
export const useNodeContextMenu = () => {
  // Context menu state
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    x: 0,
    y: 0,
    nodeId: '',
    node: null,
    show: false,
  });

  // Reference to the ReactFlow container
  const reactFlowRef = useRef<HTMLDivElement | null>(null);

  // Get the delete node function
  const deleteNode = useDeleteNode();
  
  // Get the toggle node inclusion function
  const toggleNodeInclusion = useToggleNodeInclusion();
  
  // Get nodes from the graph context
  const { nodes } = useGraph();

  // Handle node context menu
  const handleNodeContextMenu = useCallback(
    (event: MouseEvent, node: Node) => {
      // Prevent default context menu
      event.preventDefault();
      
      // Get the ReactFlow container
      const reactFlowContainer = (event.target as HTMLElement).closest('.react-flow') as HTMLDivElement;
      if (reactFlowContainer) {
        reactFlowRef.current = reactFlowContainer;
      }
      
      if (!reactFlowRef.current) return;
      
      // Get the ReactFlow container bounds
      const pane = reactFlowRef.current.getBoundingClientRect();
      
      // Calculate position of the context menu with bounds checking
      // to prevent it from going off-screen
      const x = event.clientX - pane.left;
      const y = event.clientY - pane.top;
      
      // Set context menu position and node ID
      setContextMenu({
        x,
        y,
        nodeId: node.id,
        node,
        show: true,
      });
    },
    []
  );

  // Close context menu
  const closeContextMenu = useCallback(() => {
    setContextMenu((prev) => ({ ...prev, show: false }));
  }, []);

  // Handle delete from context menu
  const handleDeleteFromContextMenu = useCallback(() => {
    // Delete the node using the nodeId from the context menu
    deleteNode(contextMenu.nodeId);
    closeContextMenu();
  }, [deleteNode, closeContextMenu, contextMenu.nodeId]);
  
  // Handle toggle inclusion from context menu
  const handleToggleInclusionFromContextMenu = useCallback(() => {
    // Toggle the inclusion state of the node
    toggleNodeInclusion(contextMenu.nodeId);
    
    // Update the node in the context menu state to reflect the change
    if (contextMenu.node) {
      const updatedNode = nodes.find(node => node.id === contextMenu.nodeId);
      if (updatedNode) {
        setContextMenu(prev => ({
          ...prev,
          node: updatedNode
        }));
      }
    }
  }, [toggleNodeInclusion, contextMenu.nodeId, contextMenu.node, nodes]);

  return {
    contextMenu,
    handleNodeContextMenu,
    closeContextMenu,
    handleDeleteFromContextMenu,
    handleToggleInclusionFromContextMenu,
    reactFlowRef,
  };
};
