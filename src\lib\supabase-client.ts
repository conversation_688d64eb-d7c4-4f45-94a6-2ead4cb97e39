/* eslint-disable no-console */
// noinspection ExceptionCaughtLocallyJS

import { createClient } from "@supabase/supabase-js";
import { Database } from "@/types";

// Keys for localStorage
const TOKEN_STORE_KEY = 'supabase_auth_token';
const SESSION_STORE_KEY = 'supabase_session_id';
const TOKEN_ERROR_KEY = 'supabase_token_error';

/**
 * Class for managing Supabase tokens
 * 
 * Singleton that manages obtaining, refreshing, and storing JWT tokens
 * for authentication in Supabase.
 */
class SupabaseTokenManager {
  private static instance: SupabaseTokenManager;
  private currentToken: string | null = null;
  private currentSessionId: string | null = null;
  private tokenExpiryTime: number | null = null;
  private refreshPromise: Promise<string | null> | null = null;
  private lastErrorTime: number | null = null;
  private supabaseUrl: string;
  private supabaseKey: string;

  private constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    this.supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    
    // Initialize from localStorage (client-side only)
    if (typeof window !== 'undefined') {
      try {
        this.currentSessionId = localStorage.getItem(SESSION_STORE_KEY);
        
        const tokenData = localStorage.getItem(TOKEN_STORE_KEY);
        const errorData = localStorage.getItem(TOKEN_ERROR_KEY);
        
        if (tokenData) {
          const parsed = JSON.parse(tokenData);
          this.currentToken = parsed.token;
          this.tokenExpiryTime = parsed.expiry;
          
          // Check if token has expired
          if (this.tokenExpiryTime && this.tokenExpiryTime < Date.now()) {
            this.clearToken();
          }
        }
        
        if (errorData) {
          this.lastErrorTime = JSON.parse(errorData).time;
        }
      } catch (error) {
        console.error('[SupabaseTokenManager] Initialization error:', error);
        this.clearToken();
      }
    }
  }

  /**
   * Get class instance (Singleton pattern)
   */
  public static getInstance(): SupabaseTokenManager {
    if (!SupabaseTokenManager.instance) {
      SupabaseTokenManager.instance = new SupabaseTokenManager();
    }
    return SupabaseTokenManager.instance;
  }

  /**
   * Get token for Supabase authentication
   * 
   * @param sessionId Optional Clerk session ID
   * @returns Promise with token or null in case of error
   */
  public async getToken(sessionId?: string): Promise<string | null> {
    // If a new sessionId is passed, update it
    if (sessionId && sessionId !== this.currentSessionId) {
      this.currentSessionId = sessionId;
      this.storeSessionId(sessionId);
      this.clearToken(); // Reset token when session changes
    }
    
    // Check if there was a recent token fetch failure (within the last 5 seconds)
    const now = Date.now();
    if (this.lastErrorTime && now - this.lastErrorTime < 5000) {
      return null;
    }
    
    // If token is missing or has expired
    if (!this.currentToken || (this.tokenExpiryTime && this.tokenExpiryTime < now)) {
      
      // If a refresh is already in progress, wait for it to complete
      if (this.refreshPromise) {
        return await this.refreshPromise;
      }
      
      // Start the process of obtaining a new token
      this.refreshPromise = this.fetchNewToken();
      try {
        const token = await this.refreshPromise;
        this.currentToken = token;
        return token;
      } catch (error) {
        console.error('[SupabaseTokenManager] Error getting token:', error);
        // Remember the time of the last error
        this.lastErrorTime = Date.now();
        this.storeErrorTime(this.lastErrorTime);
        return null;
      } finally {
        this.refreshPromise = null;
      }
    }
    
    return this.currentToken;
  }

  /**
   * Get a new token from the server
   * 
   * @returns Promise with token or null in case of error
   */
  private async fetchNewToken(): Promise<string | null> {
    try {
      
      // Check if there is an active session
      if (!this.currentSessionId) {
        return null;
      }
      
      // Use full URL with base path
      const baseUrl = typeof window !== 'undefined' 
        ? window.location.origin 
        : process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      
      const response = await fetch(`${baseUrl}/api/get-supabase-token`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Important for passing session cookies
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error getting token: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      if (!data.token) {
        throw new Error('Token not received from server');
      }
      
      // Save new token
      const token: string = data.token;
      
      // Set token expiration time (8 hours)
      this.tokenExpiryTime = Date.now() + 28800000; // 8 hours in milliseconds
      
      // Save to localStorage
      this.storeToken(token, this.tokenExpiryTime);
      this.clearErrorTime(); // Reset last error time
      
      return token;
    } catch (error) {
      console.error('[SupabaseTokenManager] Error requesting token:', error);
      throw error;
    }
  }

  /**
   * Save token to localStorage
   */
  private storeToken(token: string, expiry: number) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(TOKEN_STORE_KEY, JSON.stringify({
        token,
        expiry
      }));
    } catch (error) {
      console.error('[SupabaseTokenManager] Error saving token:', error);
    }
  }
  
  /**
   * Save session ID to localStorage
   */
  private storeSessionId(sessionId: string) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(SESSION_STORE_KEY, sessionId);
    } catch (error) {
      console.error('[SupabaseTokenManager] Error saving sessionId:', error);
    }
  }
  
  /**
   * Save last error time to localStorage
   */
  private storeErrorTime(time: number) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(TOKEN_ERROR_KEY, JSON.stringify({ time }));
    } catch (error) {
      console.error('[SupabaseTokenManager] Error saving error time:', error);
    }
  }
  
  /**
   * Clear last error time information
   */
  private clearErrorTime() {
    this.lastErrorTime = null;
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(TOKEN_ERROR_KEY);
    } catch (error) {
      console.error('[SupabaseTokenManager] Error clearing error time:', error);
    }
  }
  
  /**
   * Clear token
   */
  private clearToken() {
    this.currentToken = null;
    this.tokenExpiryTime = null;
    
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(TOKEN_STORE_KEY);
    } catch (error) {
      console.error('[SupabaseTokenManager] Error clearing token:', error);
    }
  }
  
  /**
   * Full data clearing
   */
  public clearAll() {
    this.currentToken = null;
    this.tokenExpiryTime = null;
    this.currentSessionId = null;
    this.lastErrorTime = null;
    
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(TOKEN_STORE_KEY);
      localStorage.removeItem(SESSION_STORE_KEY);
      localStorage.removeItem(TOKEN_ERROR_KEY);
    } catch (error) {
      console.error('[SupabaseTokenManager] Error during full clearing:', error);
    }
  }
}

/**
 * Types for Supabase client options
 */
type SupabaseOptions = {
  auth?: {
    autoRefreshToken: boolean;
    persistSession: boolean;
  };
  global?: {
    headers: {
      Authorization: string;
    };
  };
};

/**
 * Get Supabase client with JWT token
 * 
 * @param sessionId Optional Clerk session ID
 * @returns Promise with Supabase client
 */
export async function getSupabaseClient(sessionId?: string) {
  try {
    const tokenManager = SupabaseTokenManager.getInstance();
    const token = await tokenManager.getToken(sessionId);
    
    if (!token) {
      console.warn('[supabase-client] Token not obtained, creating anonymous client');
      return createSupabaseClient();
    }
    
    return createSupabaseClient(token);
  } catch (error) {
    console.error('[supabase-client] Error getting token:', error);
    // In case of error, return anonymous client
    return createSupabaseClient();
  }
}

/**
 * Create Supabase client
 * 
 * @param jwt Optional JWT token for authentication
 * @returns Supabase client
 */
function createSupabaseClient(jwt?: string) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Supabase environment variables are missing');
  }
  
  const options: SupabaseOptions = {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  };
  
  // If a JWT token is passed, use it
  if (jwt) {
    options.global = {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    };
  }
  
  return createClient<Database>(supabaseUrl, supabaseKey, options);
}

/**
 * Get Supabase client with service key (for admin operations)
 * 
 * @returns Supabase client with service key
 */
export function getSupabaseAdmin() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase Admin environment variables are missing');
  }
  
  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Export token manager for direct access
export const tokenManager = SupabaseTokenManager.getInstance();

/**
 * Clear all Supabase tokens
 * Used when exiting the system or when authentication issues occur
 */
export function clearSupabaseTokens(): void {
  const manager = SupabaseTokenManager.getInstance();
  manager.clearAll();
}