"use client";

import React, { createContext, useContext, useRef, ReactNode } from "react";
import { GraphComponentHandle } from "@/components/graph/GraphComponent";

interface GraphRefContextType {
  graphRef: React.RefObject<GraphComponentHandle>;
}

const GraphRefContext = createContext<GraphRefContextType | undefined>(
  undefined,
);

interface GraphRefProviderProps {
  children: ReactNode;
}

export const GraphRefProvider: React.FC<GraphRefProviderProps> = ({
  children,
}) => {
  const graphRef = useRef<GraphComponentHandle>(null);

  return (
    <GraphRefContext.Provider value={{ graphRef }}>
      {children}
    </GraphRefContext.Provider>
  );
};

export const useGraphRef = (): GraphRefContextType => {
  const context = useContext(GraphRefContext);
  if (context === undefined) {
    throw new Error("useGraphRef must be used within a GraphRefProvider");
  }
  return context;
};
