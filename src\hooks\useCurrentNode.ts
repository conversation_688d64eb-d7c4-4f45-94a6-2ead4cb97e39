import { useCallback, useMemo } from "react";
import { Node } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";

/**
 * Hook for managing the current node (selected or last added)
 * @returns Object with current node utilities
 */
export const useCurrentNode = () => {
  const { nodes, currentNodeId, setCurrentNodeId, selectedNodeId } = useGraph();

  /**
   * Get the current node based on currentNodeId or last added
   */
  const getCurrentNode = useCallback((): Node | null => {
    // If there's a currentNodeId and it exists in nodes, return that node
    if (currentNodeId && nodes.find((node) => node.id === currentNodeId)) {
      return nodes.find((node) => node.id === currentNodeId) || null;
    }

    // If there's a selectedNodeId and it exists in nodes, return that node
    if (selectedNodeId && nodes.find((node) => node.id === selectedNodeId)) {
      return nodes.find((node) => node.id === selectedNodeId) || null;
    }

    // Otherwise return the last added node if any nodes exist
    if (nodes.length > 0) {
      return nodes[nodes.length - 1];
    }

    // If no nodes exist, return null
    return null;
  }, [currentNodeId, selectedNodeId, nodes]);

  /**
   * Set a node as current
   */
  const setCurrentNode = useCallback(
    (node: Node | null) => {
      setCurrentNodeId(node ? node.id : null);
    },
    [setCurrentNodeId],
  );

  /**
   * Check if a node is the current node
   */
  const isCurrentNode = useCallback(
    (nodeId: string): boolean => {
      const current = getCurrentNode();
      return current ? current.id === nodeId : false;
    },
    [getCurrentNode],
  );

  /**
   * Clear the current node selection
   */
  const clearCurrentNode = useCallback(() => {
    setCurrentNodeId(null);
  }, [setCurrentNodeId]);

  return useMemo(
    () => ({
      currentNode: getCurrentNode(),
      getCurrentNode,
      setCurrentNode,
      isCurrentNode,
      clearCurrentNode,
      currentNodeId,
      selectedNodeId,
    }),
    [
      getCurrentNode,
      setCurrentNode,
      isCurrentNode,
      clearCurrentNode,
      currentNodeId,
      selectedNodeId,
    ],
  );
};

export default useCurrentNode;
