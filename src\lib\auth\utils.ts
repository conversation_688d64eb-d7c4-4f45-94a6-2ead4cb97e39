/* eslint-disable no-console */
/**
 * Authentication Utilities
 * 
 * This module provides utility functions for authentication.
 */

// Simple logger with timestamps and prefixes
export const logger = {
  info: (message: string, ...args: unknown[]) => {
    console.log(`[AUTH][${new Date().toISOString()}] INFO: ${message}`, ...args);
  },
  warn: (message: string, ...args: unknown[]) => {
    console.warn(`[AUTH][${new Date().toISOString()}] WARN: ${message}`, ...args);
  },
  error: (message: string, ...args: unknown[]) => {
    console.error(`[AUTH][${new Date().toISOString()}] ERROR: ${message}`, ...args);
  },
  debug: (message: string, ...args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[AUTH][${new Date().toISOString()}] DEBUG: ${message}`, ...args);
    }
  }
};

/**
 * Parse a JWT token and extract its payload
 * 
 * @param token The JWT token to parse
 * @returns The decoded payload or null if invalid
 */
export const parseJwt = (token: string): Record<string, unknown> | null => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    logger.error('Error parsing JWT:', error);
    return null;
  }
};

/**
 * Check if a JWT token is expired
 * 
 * @param token The JWT token to check
 * @param bufferSeconds Optional buffer time in seconds (default: 300 - 5 minutes)
 * @returns Boolean indicating if the token is expired or will expire soon
 */
export const isTokenExpired = (token: string, bufferSeconds = 300): boolean => {
  try {
    const payload = parseJwt(token);
    
    if (!payload || typeof payload.exp !== 'number') {
      return true;
    }
    
    // exp is in seconds, Date.now() is in milliseconds
    const expiryTime = payload.exp * 1000;
    const currentTime = Date.now();
    
    // Check if the token is expired or will expire within the buffer time
    return expiryTime <= currentTime + bufferSeconds * 1000;
  } catch (error) {
    logger.error('Error checking token expiry:', error);
    return true;
  }
};

/**
 * Get the user ID from a JWT token
 * 
 * @param token The JWT token
 * @returns The user ID or null if not found
 */
export const getUserIdFromToken = (token: string): string | null => {
  try {
    const payload = parseJwt(token);
    
    if (!payload || typeof payload.sub !== 'string') {
      return null;
    }
    
    return payload.sub;
  } catch (error) {
    logger.error('Error getting user ID from token:', error);
    return null;
  }
};
