"use client";

import React from "react";
import { ClerkProvider } from "@clerk/nextjs";
import { useTheme } from "next-themes";
import { dark } from "@clerk/themes";

export const ClerkThemeCustomProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === "dark";
  
  return (
    <ClerkProvider
      appearance={{
        baseTheme: isDarkMode ? dark : undefined,
        variables: {
          colorPrimary: "rgb(34, 197, 94)",
        },
        elements: {
          formButtonPrimary:
            "bg-green-500 hover:bg-green-600 text-sm normal-case",
          socialButtonsBlockButton:
            "bg-white border-gray-200 hover:bg-gray-50 text-gray-800 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600 dark:text-white",
          card: "bg-white dark:bg-gray-800 shadow-none",
          headerTitle: "text-gray-900 dark:text-white",
          headerSubtitle: "text-gray-500 dark:text-gray-400",
          dividerLine: "bg-gray-200 dark:bg-gray-700",
          dividerText: "text-gray-500 dark:text-gray-400",
          formFieldLabel: "text-gray-700 dark:text-gray-300",
          formFieldInput:
            "bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white",
          footerActionLink: "text-green-500 hover:text-green-600",
          identityPreviewEditButton: "text-green-500 hover:text-green-600",
        },
      }}
      signInUrl="/login"
      signUpUrl="/signup"
    >
      {children}
    </ClerkProvider>
  );
};
