"use client";

import { SignUp } from "@clerk/nextjs";
import Link from "next/link";
import { ROUTES, SIGNUP_PAGE } from "@/lib/constants";

export default function SignUpPage(): JSX.Element {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">{SIGNUP_PAGE.TITLE}</h1>
          <p className="mt-2 text-gray-600">{SIGNUP_PAGE.SUBTITLE}</p>
        </div>

        <SignUp
          appearance={{
            elements: {
              formButtonPrimary:
                "bg-blue-500 hover:bg-blue-600 text-sm normal-case",
              card: "shadow-md",
            },
          }}
        />
        
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link href={ROUTES.LOGIN} className="text-blue-500 hover:text-blue-600 font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
