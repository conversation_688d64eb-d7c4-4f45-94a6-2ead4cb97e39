import {useCallback} from "react";
import {Edge} from "@xyflow/react";
import {useGraph} from "@/contexts/GraphContext";
import {useGraphHistory} from "@/contexts/GraphHistoryContext";
import {NODE} from "@/lib/constants";
import {useNodeAncestors} from "./useNodeAncestors";
import {EdgeBuilder} from "@/utils/edgeBuilder";
import {EdgeTagFilter} from "@/types/tagFilter";

/**
 * Custom hook to manage edge blocking functionality
 * Provides functions to block/unblock edges or toggle their blocked state
 */
export const useBlockEdgeConnection = () => {
  const {
    nodes,
    edges,
    setEdges,
    setNodes,
    selectedNodeId,
    currentNodeId,
    bgColor,
    messages,
  } = useGraph();
  
  const { saveToHistory } = useGraphHistory();
  const getAncestors = useNodeAncestors();

  /**
   * Block or unblock an edge by its ID
   * @param edgeId - The ID of the edge to block/unblock
   * @param isBlocked - Whether to block (true) or unblock (false) the edge
   */
  const setEdgeBlocked = useCallback(
    (edgeId: string, isBlocked: boolean) => {
      // Save current state to history before modifying the edge
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Создаем обновленный список ребер с учетом новой блокировки
      const updatedEdges = edges.map(edge => {
        if (edge.id === edgeId) {
          return { 
            ...edge, 
            data: { 
              ...edge.data, 
              isBlocked 
            }
          };
        }
        return edge;
      });

      // После изменения состояния блокировки, обновляем стили узлов и ребер
      if (currentNodeId) {
        // Получаем обновленный список предков с учетом блокировки
        const ancestors = getAncestors(currentNodeId, nodes, updatedEdges);
        const ancestorIds = new Set([currentNodeId, ...ancestors.map(a => a.id)]);
        
        // Создаем множество ребер, входящих в путь
        const pathEdgeIds = new Set();

        // Определяем, какие ребра входят в путь
        updatedEdges.forEach((edge) => {
          if (ancestorIds.has(edge.source) && ancestorIds.has(edge.target)) {
            pathEdgeIds.add(edge.id);
          }
        });
        
        // Обновляем стили узлов
        setNodes(nds => 
          nds.map(node => {
            const isInPath = ancestorIds.has(node.id);
            return {
              ...node,
              style: {
                ...node.style,
                border: isInPath ? NODE.BORDER.HIGHLIGHTED : NODE.BORDER.DEFAULT,
                background: NODE.BACKGROUNDS.DEFAULT,
              },
              data: {
                ...node.data,
                isInPath,
              },
            };
          })
        );
        
        // Обновляем стили всех ребер
        setEdges(eds => 
          eds.map(edge => {
            // Проверяем, входит ли ребро в путь
            const isInPath = pathEdgeIds.has(edge.id);
            // Проверяем, заблокировано ли ребро
            const edgeIsBlocked = edge.id === edgeId ? isBlocked : edge.data?.isBlocked === true;
            
            // Получаем фильтр тегов для этого ребра
            const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
            
            // Проверяем, имеет ли ребро фильтр
            // Ребро фильтруется, если имеет фильтр
            const isFiltered = tagFilter.length > 0;
            
            // Используем helper метод для получения стилей
            const edgeStyles = EdgeBuilder.getEdgeStyles({
              isBlocked: edgeIsBlocked,
              isInPath,
              isSelected: edge.selected || false,
              isFiltered
            });
            
            return {
              ...edge,
              style: edgeStyles.style,
              animated: edgeStyles.animated,
              markerEnd: edgeStyles.markerEnd,
              data: {
                ...edge.data,
                isInPath,
                isBlocked: edgeIsBlocked,
              },
            };
          })
        );
      } else {
        // Если нет выбранного узла, просто обновляем стиль заблокированного ребра
        setEdges((eds: Edge[]) =>
          eds.map((edge) => {
            if (edge.id === edgeId) {
              // Check if this edge is part of an active path
              const isInPath = edge.data?.isInPath === true;
              
              // Получаем фильтр тегов для этого ребра
              const tagFilter = edge.data?.tagFilter as EdgeTagFilter || [];
              
              // Проверяем, имеет ли ребро фильтр
              // Ребро фильтруется, если имеет фильтр
              const isFiltered = tagFilter.length > 0;
              
              // Используем helper метод для получения стилей
              const edgeStyles = EdgeBuilder.getEdgeStyles({
                isBlocked,
                isInPath,
                isSelected: edge.selected || false,
                isFiltered
              });
              
              return {
                ...edge,
                style: edgeStyles.style,
                animated: edgeStyles.animated,
                markerEnd: edgeStyles.markerEnd,
                data: {
                  ...edge.data,
                  isBlocked,
                },
              };
            }
            return edge;
          })
        );
      }
    },
    [
      nodes,
      edges,
      setEdges,
      setNodes,
      saveToHistory,
      bgColor,
      selectedNodeId,
      currentNodeId,
      messages,
      getAncestors
    ]
  );

  /**
   * Toggle the blocked state of an edge
   * @param edgeId - The ID of the edge to toggle
   */
  const toggleEdgeBlocked = useCallback(
    (edgeId: string) => {
      const edge = edges.find((e) => e.id === edgeId);
      if (edge) {
        const currentBlockedState = edge.data?.isBlocked || false;
        setEdgeBlocked(edgeId, !currentBlockedState);
      }
    },
    [edges, setEdgeBlocked]
  );

  /**
   * Block an edge
   * @param edgeId - The ID of the edge to block
   */
  const blockEdgeConnection = useCallback(
    (edgeId: string) => {
      setEdgeBlocked(edgeId, true);
    },
    [setEdgeBlocked]
  );

  /**
   * Unblock an edge
   * @param edgeId - The ID of the edge to unblock
   */
  const unblockEdgeConnection = useCallback(
    (edgeId: string) => {
      setEdgeBlocked(edgeId, false);
    },
    [setEdgeBlocked]
  );

  return {
    blockEdgeConnection,
    unblockEdgeConnection,
    toggleEdgeBlocked,
    setEdgeBlocked,
  };
};
