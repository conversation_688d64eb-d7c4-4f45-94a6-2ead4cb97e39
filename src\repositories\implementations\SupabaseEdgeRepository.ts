import { getSupabaseClient } from "@/lib/supabase-client";
import { IEdgeRepository } from "@/repositories";
import { EdgeDTO as Edge, CreateEdgeDTO, UpdateEdgeDTO } from "@/types/dto";

/**
 * Supabase implementation of the Edge repository
 */
export class SupabaseEdgeRepository implements IEdgeRepository {
  private supabase;

  constructor() {
    this.supabase = getSupabaseClient();
  }

  /**
   * Find an edge by ID
   */
  async findById(id: string): Promise<Edge | null> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_node_relationships')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return null;

    return this.mapToEdge(data);
  }

  /**
   * Find all edges for a graph
   */
  async findByGraphId(graphId: string): Promise<Edge[]> {
    // Since the relationships table doesn't have a direct graph_id,
    // we need to join with nodes to get relationships for a specific graph
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_node_relationships')
      .select(`
        *,
        chat_nodes_chat_node_relationships_parent_idTochat_nodes!inner(
          graph_id
        )
      `)
      .eq('chat_nodes_chat_node_relationships_parent_idTochat_nodes.graph_id', graphId);

    if (error || !data) return [];

    return data.map(this.mapToEdge);
  }

  /**
   * Find all edges where the specified node is the source
   */
  async findBySourceId(nodeId: string): Promise<Edge[]> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_node_relationships')
      .select('*')
      .eq('parent_id', nodeId);

    if (error || !data) return [];

    return data.map(this.mapToEdge);
  }

  /**
   * Find all edges where the specified node is the target
   */
  async findByTargetId(nodeId: string): Promise<Edge[]> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_node_relationships')
      .select('*')
      .eq('child_id', nodeId);

    if (error || !data) return [];

    return data.map(this.mapToEdge);
  }

  /**
   * Create a new edge
   */
  async create(data: CreateEdgeDTO): Promise<Edge> {
    // Prepare edge data
    const edgeData: Record<string, unknown> = {
      parent_id: data.sourceId,
      child_id: data.targetId,
      label: data.label || null,
      type: data.type || 'default',
      is_blocked: data.isBlocked || false,
      tag_filter: data.tagFilter || [],
      data: data.data || {}
    };

    // Create the edge
    const client = await this.supabase;
    const { data: newEdge, error } = await client
      .from('chat_node_relationships')
      .insert(edgeData)
      .select()
      .single();

    if (error || !newEdge) {
      throw new Error(`Failed to create edge: ${error?.message}`);
    }

    return this.mapToEdge(newEdge);
  }

  /**
   * Update an existing edge
   */
  async update(id: string, data: UpdateEdgeDTO): Promise<Edge> {
    const updateData: Record<string, unknown> = {};

    if (data.sourceId !== undefined) updateData.parent_id = data.sourceId;
    if (data.targetId !== undefined) updateData.child_id = data.targetId;
    if (data.label !== undefined) updateData.label = data.label;
    if (data.type !== undefined) updateData.type = data.type;
    if (data.isBlocked !== undefined) updateData.is_blocked = data.isBlocked;
    if (data.tagFilter !== undefined) updateData.tag_filter = data.tagFilter;
    if (data.data !== undefined) updateData.data = data.data;

    const client = await this.supabase;
    const { data: updatedEdge, error } = await client
      .from('chat_node_relationships')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !updatedEdge) {
      throw new Error(`Failed to update edge: ${error?.message}`);
    }

    return this.mapToEdge(updatedEdge);
  }

  /**
   * Delete an edge
   */
  async delete(id: string): Promise<boolean> {
    const client = await this.supabase;
    const { error } = await client
      .from('chat_node_relationships')
      .delete()
      .eq('id', id);

    return !error;
  }

  /**
   * Map Supabase data to Edge
   */
  private mapToEdge(data: Record<string, unknown>): Edge {
    // Get the graph ID from the parent node if available
    const graphId = (data.chat_nodes_chat_node_relationships_parent_idTochat_nodes as Record<string, unknown>)?.graph_id as string || '';

    return {
      id: data.id as string,
      sourceId: data.parent_id as string,
      targetId: data.child_id as string,
      graphId,
      label: data.label as string || null,
      type: data.type as string || 'default',
      isBlocked: data.is_blocked as boolean || false,
      tagFilter: data.tag_filter as string[] || [],
      data: data.data as Record<string, unknown> || {},
      createdAt: new Date(data.created_at as string),
      updatedAt: new Date((data.updated_at || data.created_at) as string) // Fallback to created_at if updated_at is not available
    };
  }
}
