import React from "react";
import { NavigationBar } from "@/components/graph/NavigationBar";
import { Header } from "@/components/graph/Header";
import { Footer } from "@/components/layout/Footer";
import { GraphRefProvider } from "@/contexts/GraphRefContext";
import { SplitContent } from "@/components/graph/SplitContent";

interface SplitViewProps {
  graphId: string;
}

export const SplitView: React.FC<SplitViewProps> = ({ graphId }) => {

  return (
    <GraphRefProvider>
      <div className="flex flex-col h-screen w-screen max-h-screen max-w-screen overflow-hidden bg-black text-white">
        {/* Header - height: 60px */}
        <Header graphId={graphId} />

        {/* Navigation - height: 40px */}
        <NavigationBar graphId={graphId} />

        {/* Main Content - Split View */}
        <SplitContent graphId={graphId} />

        {/* Footer with Save button */}
        <Footer graphId={graphId} />
      </div>
    </GraphRefProvider>
  );
};
