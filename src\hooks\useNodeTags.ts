/* eslint-disable no-console */

import { useCallback, useMemo } from 'react';
import { Node } from '@xyflow/react';
import { useGraph } from '@/contexts/GraphContext';
import { useGraphHistory } from '@/contexts/GraphHistoryContext';
import { NODE_TAGS } from '@/lib/constants';
import { NodeTag } from '@/utils/nodeBuilder';

/**
 * Hook for managing node tags
 */
export const useNodeTags = () => {
  const { nodes, edges, selectedNodeId, currentNodeId, bgColor, messages, setNodes } = useGraph();
  const { saveToHistory } = useGraphHistory();

  // Create a graph state object for saving to history
  const createHistoryState = useCallback(() => {
    return {
      nodes,
      edges,
      selectedNodeId,
      currentNodeId,
      bgColor,
      messages
    };
  }, [nodes, edges, selectedNodeId, currentNodeId, bgColor, messages]);

  /**
   * Creates a new tag
   * @param id Tag identifier
   * @param icon Icon name
   * @param label Display name (optional)
   * @param color Tag color (optional)
   * @returns Tag object
   */
  const createTag = useCallback((
    id: string, 
    icon: string,
    label?: string, 
    color?: string
  ): NodeTag => {
    return {
      id,
      icon,
      label: label || id,
      color: color || NODE_TAGS.DEFAULT_COLOR,
    };
  }, []);

  /**
   * Adds a tag to a node
   * @param nodeId Node ID
   * @param tag Tag to add or its ID
   * @param label Display name (if only ID is provided)
   * @param color Tag color (if only ID is provided)
   */
  const addTagToNode = useCallback((
    nodeId: string, 
    tag: string | NodeTag,
    label?: string,
    color?: string
  ) => {
    
    // Save current state to history
    saveToHistory(createHistoryState());

    setNodes((prevNodes) => {
      const updatedNodes = prevNodes.map((node) => {
        if (node.id === nodeId) {
         
          // Create a new tags array
          const currentTags: NodeTag[] = Array.isArray(node.data?.tags) 
            ? [...node.data.tags as NodeTag[]]
            : [];
          
          // Determine the tag to add
          let tagToAdd: NodeTag;
          
          if (typeof tag === 'string') {
            // Check if this tag exists in predefined tags
            const defaultTag = NODE_TAGS.DEFAULTS.find(t => t.id === tag);
            
            if (defaultTag) {
              tagToAdd = { ...defaultTag };
            } else {
              // Create a new tag
              tagToAdd = createTag(tag, 'VscInfo', label || tag, color);
            }
          } else {
            // Use the provided tag object
            tagToAdd = { ...tag };
          }
          
          
          // Check if the tag already exists
          const tagExists = currentTags.some((t: NodeTag) => t.id === tagToAdd.id);
          
          // If the tag doesn't exist, add it
          const newTags = tagExists 
            ? currentTags 
            : [...currentTags, tagToAdd];
          
          // Return the updated node
          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              tags: newTags,
            },
          };
          
          return updatedNode;
        }
        return node;
      });
      
      return updatedNodes;
    });
  }, [setNodes, saveToHistory, createHistoryState, createTag]);

  /**
   * Removes a tag from a node
   * @param nodeId Node ID
   * @param tagId Tag ID to remove
   */
  const removeTagFromNode = useCallback((nodeId: string, tagId: string) => {
    
    // Find the node to remove the tag from
    const node = nodes.find((n) => n.id === nodeId);
    if (!node) {
      console.error(`Node with ID ${nodeId} not found`);
      return;
    }
    
    // Get the current tags
    const currentTags: NodeTag[] = Array.isArray(node?.data?.tags) 
      ? [...node.data.tags as NodeTag[]]
      : [];
    
    // Filter out the tag to remove
    const updatedTags = currentTags.filter((tag) => tag.id !== tagId);
        
    // Save current state to history
    saveToHistory(createHistoryState());
    
    // Update the node with the new tags
    setNodes((nds) => 
      nds.map((n) => {
        if (n.id === nodeId) {
          return {
            ...n,
            data: {
              ...n.data,
              tags: updatedTags.length > 0 ? updatedTags : undefined,
            },
          };
        }
        return n;
      })
    );
  }, [nodes, setNodes, saveToHistory, createHistoryState]);

  /**
   * Toggles a tag on a node (adds if not present; removes if present)
   * @param nodeId Node ID
   * @param tagId Tag ID or tag object to toggle
   * @param label Display name (optional)
   * @param color Tag color (optional)
   */
  const toggleNodeTag = useCallback((
    nodeId: string, 
    tagId: string | NodeTag,
    label?: string,
    color?: string
  ) => {
    // Find the node to check if it already has this tag
    const node = nodes.find((n) => n.id === nodeId);
    if (!node) {
      console.error(`Node with ID ${nodeId} not found`);
      return;
    }
    
    const currentTags: NodeTag[] = Array.isArray(node?.data?.tags) 
      ? [...node.data.tags as NodeTag[]]
      : [];
    
    const id = typeof tagId === 'string' ? tagId : tagId.id;
    
    if (currentTags.some((tag: NodeTag) => tag.id === id)) {
      removeTagFromNode(nodeId, id);
    } else {
      addTagToNode(nodeId, tagId, label, color);
    }
  }, [nodes, addTagToNode, removeTagFromNode]);

  /**
   * Gets all tags of a node
   * @param nodeId Node ID
   * @returns Array of node tags or empty array
   */
  const getNodeTags = useCallback((nodeId: string): NodeTag[] => {
    const node = nodes.find((n) => n.id === nodeId);
    return Array.isArray(node?.data?.tags) ? node?.data?.tags as NodeTag[] : [];
  }, [nodes]);

  /**
   * Checks if a node has a specific tag
   * @param nodeId Node ID
   * @param tagId Tag ID to check
   * @returns true if the node has the specified tag
   */
  const hasNodeTag = useCallback((nodeId: string, tagId: string): boolean => {
    const tags = getNodeTags(nodeId);
    return tags.some((tag: NodeTag) => tag.id === tagId);
  }, [getNodeTags]);

  /**
   * Gets all nodes with a specific tag
   * @param tagId Tag ID to search for
   * @returns Array of nodes with the specified tag
   */
  const getNodesByTag = useCallback((tagId: string): Node[] => {
    return nodes.filter((node) => {
      const tags: NodeTag[] = Array.isArray(node.data?.tags) 
        ? node.data?.tags as NodeTag[] 
        : [];
      return tags.some((tag: NodeTag) => tag.id === tagId);
    });
  }, [nodes]);

  /**
   * Gets a list of all tags used in the graph
   * @returns Array of unique tags
   */
  const getAllUsedTags = useCallback((): NodeTag[] => {
    const tagsMap = new Map<string, NodeTag>();
    
    nodes.forEach((node) => {
      const tags: NodeTag[] = Array.isArray(node.data?.tags) 
        ? node.data?.tags as NodeTag[] 
        : [];
      tags.forEach((tag: NodeTag) => {
        if (!tagsMap.has(tag.id)) {
          tagsMap.set(tag.id, tag);
        }
      });
    });
    
    return Array.from(tagsMap.values());
  }, [nodes]);

  /**
   * Gets a list of predefined tags
   * @returns Array of predefined tags
   */
  const getDefaultTags = useCallback((): NodeTag[] => {
    return [...NODE_TAGS.DEFAULTS];
  }, []);

  /**
   * Updates a node tag
   * @param nodeId Node ID
   * @param tagId Tag ID to update
   * @param updates Updates for the tag
   */
  const updateNodeTag = useCallback((
    nodeId: string, 
    tagId: string, 
    updates: Partial<Omit<NodeTag, 'id'>>
  ) => {
    // Save current state to history
    saveToHistory(createHistoryState());

    setNodes((prevNodes) => 
      prevNodes.map((node) => {
        if (node.id === nodeId) {
          const currentTags: NodeTag[] = Array.isArray(node.data?.tags) 
            ? node.data?.tags as NodeTag[] 
            : [];
          const tagIndex = currentTags.findIndex((t: NodeTag) => t.id === tagId);
          
          if (tagIndex === -1) return node;
          
          // Create a copy of the tags array
          const newTags = [...currentTags];
          
          // Update the tag
          newTags[tagIndex] = {
            ...newTags[tagIndex],
            ...updates,
          };
          
          // Return the updated node
          return {
            ...node,
            data: {
              ...node.data,
              tags: newTags,
            },
          };
        }
        return node;
      })
    );
  }, [setNodes, saveToHistory, createHistoryState]);

  return useMemo(() => ({
    createTag,
    addTagToNode,
    removeTagFromNode,
    toggleNodeTag,
    getNodeTags,
    hasNodeTag,
    getNodesByTag,
    getAllUsedTags,
    getDefaultTags,
    updateNodeTag,
  }), [
    createTag,
    addTagToNode,
    removeTagFromNode,
    toggleNodeTag,
    getNodeTags,
    hasNodeTag,
    getNodesByTag,
    getAllUsedTags,
    getDefaultTags,
    updateNodeTag,
  ]);
};

export default useNodeTags;
