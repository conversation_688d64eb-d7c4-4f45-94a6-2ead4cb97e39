import { EdgeDTO as Edge, CreateEdgeDTO, UpdateEdgeDTO } from "@/types/dto";

/**
 * Interface for Edge repository
 * Defines methods for interacting with edge data (relationships between nodes)
 */
export interface IEdgeRepository {
  /**
   * Find an edge by ID
   * @param id - The edge ID
   * @returns The edge or null if not found
   */
  findById(id: string): Promise<Edge | null>;
  
  /**
   * Find all edges for a specific graph
   * @param graphId - The graph ID
   * @returns Array of edges
   */
  findByGraphId(graphId: string): Promise<Edge[]>;
  
  /**
   * Find all edges where the specified node is the source
   * @param nodeId - The source node ID
   * @returns Array of edges
   */
  findBySourceId(nodeId: string): Promise<Edge[]>;
  
  /**
   * Find all edges where the specified node is the target
   * @param nodeId - The target node ID
   * @returns Array of edges
   */
  findByTargetId(nodeId: string): Promise<Edge[]>;
  
  /**
   * Create a new edge
   * @param data - The edge data
   * @returns The created edge
   */
  create(data: CreateEdgeDTO): Promise<Edge>;
  
  /**
   * Update an existing edge
   * @param id - The edge ID
   * @param data - The updated edge data
   * @returns The updated edge
   */
  update(id: string, data: UpdateEdgeDTO): Promise<Edge>;
  
  /**
   * Delete an edge
   * @param id - The edge ID
   * @returns True if successful, false otherwise
   */
  delete(id: string): Promise<boolean>;
}
