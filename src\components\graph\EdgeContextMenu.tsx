"use client";

import React, { useEffect, useState } from 'react';
import { FiTrash2, <PERSON><PERSON>ock, <PERSON>Unlock, Fi<PERSON>ilter, FiX } from 'react-icons/fi';
import { useNodeTags } from '@/hooks/useNodeTags';
import { EdgeTagFilter } from '@/types/tagFilter';
import { NodeTag } from '@/utils/nodeBuilder';
// Import icons
import { LiaThumbtackSolid } from 'react-icons/lia';
import { FaRegThumbsUp, FaRegThumbsDown, FaRegLightbulb } from 'react-icons/fa';
import { VscError, VscInfo } from 'react-icons/vsc';
import { BiSolidShow, BiSolidHide } from 'react-icons/bi';
import { BsFire } from 'react-icons/bs';

// Icon map for access by name
const iconMap: Record<string, React.ComponentType> = {
  LiaThumbtackSolid,
  FaRegThumbsUp,
  FaRegThumbsDown,
  FaRegLightbulb,
  VscError,
  VscInfo,
  BiSolidShow,
  BiSolidHide,
  BsFire
};

interface EdgeContextMenuProps {
  x: number;
  y: number;
  edgeId: string;
  onDelete: () => void;
  onClose: () => void;
  onToggleBlock: () => void;
  onSetTagFilter: (filter: EdgeTagFilter | null) => void;
  onToggleTag?: (tagId: string) => void;
  isBlocked?: boolean;
  tagFilter?: EdgeTagFilter | null;
}

/**
 * Context menu component for edges
 * Displays a menu with options when right-clicking on an edge
 */
export const EdgeContextMenu: React.FC<EdgeContextMenuProps> = ({
  x,
  y,
  edgeId,
  onDelete,
  onClose,
  onToggleBlock,
  onSetTagFilter,
  onToggleTag,
  isBlocked = false,
  tagFilter = null,
}) => {
  const [showTagFilterMenu, setShowTagFilterMenu] = useState(false);
  const { getDefaultTags } = useNodeTags();
  const defaultTags = getDefaultTags();
  
  // Normalize tagFilter to be an array
  const normalizedTagFilter = tagFilter || [];

  // Handle clicks outside the menu to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Don't close if clicking inside the menu
      if ((event.target as HTMLElement).closest('[data-edgeid]')) {
        return;
      }
      onClose();
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [onClose]);

  // Calculate if menu should be positioned differently to avoid going off-screen
  const menuStyle: React.CSSProperties = {
    position: 'absolute',
    zIndex: 1000,
    // Position the menu at the cursor location
    left: x,
    top: y,
    minWidth: '180px',
  };

  // Check if a tag is used in the current filter
  const isTagInFilter = (tagId: string): boolean => {
    return normalizedTagFilter.includes(tagId);
  };

  // Handle tag selection for filtering
  const handleTagSelect = (tag: NodeTag) => {
    if (onToggleTag) {
      onToggleTag(tag.id);
    } else {
      // If onToggleTag is not provided, use the old method
      // If tag is already in filter, remove it, otherwise add it
      const newFilter = isTagInFilter(tag.id)
        ? normalizedTagFilter.filter(id => id !== tag.id)
        : [...normalizedTagFilter, tag.id];
      
      onSetTagFilter(newFilter.length > 0 ? newFilter : null);
    }
  };

  // Remove tag filter
  const handleRemoveFilter = () => {
    onSetTagFilter([]);
    onClose();
  };

  // Get icon component by name
  const getIconComponent = (iconName: string) => {
    const IconComponent = iconMap[iconName];
    if (IconComponent) {
      return <IconComponent />;
    }
    return null;
  };

  // If showing tag filter menu, render the tag selection interface
  if (showTagFilterMenu) {
    return (
      <div 
        className="bg-gray-800 border border-gray-700 rounded-md shadow-lg p-3"
        style={menuStyle}
        onClick={(e) => e.stopPropagation()}
        data-edgeid={edgeId}
      >
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-white text-sm font-medium">Filter by Tag</h3>
          <button 
            className="text-gray-400 hover:text-white"
            onClick={() => setShowTagFilterMenu(false)}
          >
            <FiX size={16} />
          </button>
        </div>
        
        <p className="text-gray-400 text-xs mb-2">
          Select tags to filter this connection. Only nodes with at least one of the selected tags will be allowed.
        </p>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {defaultTags.map((tag) => (
            <button
              key={tag.id}
              className={`inline-flex items-center justify-center p-1.5 rounded-full text-xs font-medium cursor-pointer hover:opacity-90 transition-opacity shadow-sm ${
                isTagInFilter(tag.id) ? 'ring-1 ring-white ring-opacity-70' : 'opacity-80'
              }`}
              style={{ 
                backgroundColor: tag.color || '#3b82f6',
                color: 'white',
              }}
              onClick={() => handleTagSelect(tag)}
              title={tag.label || tag.id}
            >
              {getIconComponent(tag.icon)}
            </button>
          ))}
        </div>
      </div>
    );
  }

  // Determine if there's an active tag filter
  const hasTagFilter = normalizedTagFilter.length > 0;

  return (
    <div 
      className="bg-gray-800 border border-gray-700 rounded-md shadow-lg"
      style={menuStyle}
      onClick={(e) => e.stopPropagation()}
      data-edgeid={edgeId}
    >
      <div className="py-1">
        <button
          className="w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center"
          onClick={() => {
            onToggleBlock();
            onClose();
          }}
        >
          {isBlocked ? (
            <>
              <FiUnlock className="mr-2 size-5" />
              Unblock Connection
            </>
          ) : (
            <>
              <FiLock className="mr-2 size-5" />
              Block Connection
            </>
          )}
        </button>
        
        {/* Tag filtering options */}
        <button
          className={`w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center ${hasTagFilter ? 'text-blue-400' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            setShowTagFilterMenu(true);
          }}
        >
          <FiFilter className="mr-2 size-5" />
          {hasTagFilter ? `Tag Filter (${normalizedTagFilter.length})` : 'Add Tag Filter'}
        </button>
        
        {/* Option to remove tag filter if one exists */}
        {hasTagFilter && (
          <button
            className="w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center"
            onClick={handleRemoveFilter}
          >
            <FiX className="mr-2 size-5" />
            Remove Tag Filter
          </button>
        )}
        
        <div className="border-t border-gray-700 my-1"></div>
        
        <button
          className="w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700 flex items-center"
          onClick={() => {
            onDelete();
            onClose();
          }}
        >
          <FiTrash2 className="mr-2 size-5" />
          Delete Connection
        </button>
      </div>
    </div>
  );
};
