import React from "react";

interface ChatErrorProps {
  /**
   * Error object containing a message
   */
  error: Error | undefined;
}

/**
 * Component that displays chat error messages
 */
export const ChatError: React.FC<ChatErrorProps> = ({ error }) => {
  if (!error) return null;

  return (
    <div className="p-3 bg-red-900 text-red-100 mx-4 mb-2 rounded-lg">
      <p>Error: {error.message}</p>
    </div>
  );
};
