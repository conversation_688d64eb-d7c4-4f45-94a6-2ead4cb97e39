import { useCallback, MouseEvent as ReactMouseEvent } from "react";
import { Node } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";

/**
 * Custom hook to handle node click events
 * Updates the selected node ID in the graph store
 */
export const useSelectNode = () => {
  const { setCurrentNodeId, setSelectedNodeId } = useGraph();

  // Handle node click
  return useCallback(
    (event: ReactMouseEvent, node: Node): void => {
      // Update both the selected node and the current node in context
      setSelectedNodeId(node.id);
      setCurrentNodeId(node.id);
    },
    [setCurrentNodeId, setSelectedNodeId],
  );
};
