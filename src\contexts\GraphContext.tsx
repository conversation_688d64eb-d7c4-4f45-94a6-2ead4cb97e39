/**
 * GraphContext
 * 
 * Context for managing graph state
 */

"use client";

import React, {
  createContext,
  use,
  useMemo,
  useRef,
  type ReactNode,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  type ReactFlowInstance,
  type Node,
  type Edge,
  type NodeChange,
  type EdgeChange,
  BackgroundVariant,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
} from "@xyflow/react";
import { useGraphInteractivity } from "@/hooks/useGraphInteractivity";
import { GraphDimensionsProvider } from "./GraphDimensionsContext";
import { GraphHistoryProvider } from "./GraphHistoryContext";
import { type Message } from "@ai-sdk/react";

// Context type definition
interface GraphContextType {
  setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
  setEdges: (edges: Edge[] | ((edges: Edge[]) => Edge[])) => void;
  nodes: Node[];
  edges: Edge[];
  selectedNodeId: string | null;
  setSelectedNodeId: (id: string | null) => void;
  bgColor: string;
  setBgColor: (bgColor: string) => void;

  // Event handlers
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  // Viewport operations
  zoomIn: () => void;
  zoomOut: () => void;
  fitView: () => void;

  // Interactivity toggle
  toggleInteractivity: () => void;
  isInteractive: boolean;
  setIsInteractive: React.Dispatch<React.SetStateAction<boolean>>;

  // Pan toggle
  togglePan: () => void;
  isPanEnabled: boolean;
  setIsPanEnabled: React.Dispatch<React.SetStateAction<boolean>>;

  // ReactFlow instance
  reactFlowInstanceRef: React.MutableRefObject<ReactFlowInstance | null>;
  defaultViewport: { x: number; y: number; zoom: number };
  backgroundVariant: BackgroundVariant;

  // Messages
  messages: Message[];
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;

  // Current node tracking
  currentNodeId: string | null;
  setCurrentNodeId: (id: string | null) => void;
  
  // Graph metadata
  graphId: string | null;
  graphTitle: string | null;
  setGraphTitle: (title: string) => void;
  
  // Sort mode
  sortByDepth: boolean;
  setSortByDepth: React.Dispatch<React.SetStateAction<boolean>>;
  toggleSortMode: () => void;
}

// Initial background color
const initBgColor = "#1F2937";
// Define constants for default viewport and background
const defaultViewport = { x: 0, y: 0, zoom: 1.2 };
const backgroundVariant = BackgroundVariant.Dots;

// Create the context
const GraphContext = createContext<GraphContextType | undefined>(undefined);

// Custom hook to use the graph context
export const useGraph = (): GraphContextType => {
  const context = use(GraphContext);
  if (!context) {
    throw new Error("useGraph must be used within a GraphProvider");
  }
  return context;
};

// Interface for initial graph data
interface GraphData {
  id: string;
  title: string;
  description?: string;
  nodes?: Node[];
  edges?: Edge[];
  bgColor?: string;
  settings?: {
    nodes?: Node[];
    edges?: Edge[];
    bgColor?: string;
    sortByDepth?: boolean;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

// Context provider component
export const GraphProvider: React.FC<{ 
  children: ReactNode;
  initialGraph?: GraphData | null;
}> = ({
  children,
  initialGraph = null,
}) => {
  // Use ReactFlow hooks for state management
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>(initialGraph?.nodes || []);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>(initialGraph?.edges || []);

  // Use React state for selected node and background color
  const [bgColor, setBgColor] = React.useState<string>(initBgColor);

  const [sortByDepth, setSortByDepth] = useState<boolean>(
    initialGraph?.settings?.sortByDepth || false,
  );

  const toggleSortMode = useCallback(() => {
    setSortByDepth(prev => !prev);
  }, []);
  

  // Reference to the ReactFlow instance
  // Internal state for messages
  const [messages, setMessages] = useState<Message[]>([]);

  // State for tracking the current node (selected or last added)
  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);

  // State for tracking the selected node (moved from GraphStore)
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  
  // Graph metadata
  const [graphId, setGraphId] = useState<string | null>(initialGraph?.id || null);
  const [graphTitle, setGraphTitle] = useState<string | null>(initialGraph?.title || null);

  const reactFlowInstanceRef = useRef<ReactFlowInstance | null>(null);

  // Graph initialization when initialGraph changes
  useEffect(() => {
    if (initialGraph) {
      setGraphId(initialGraph.id);
      setGraphTitle(initialGraph.title);
      
      // Check if nodes and edges exist directly in initialGraph
      if (initialGraph.nodes && initialGraph.nodes.length > 0) {
        setNodes(initialGraph.nodes);
      } 
      // If nodes don't exist directly, check in settings
      else if (initialGraph.settings?.nodes && initialGraph.settings.nodes.length > 0) {
        setNodes(initialGraph.settings.nodes);
      }
      
      if (initialGraph.edges && initialGraph.edges.length > 0) {
        setEdges(initialGraph.edges);
      }
      // If edges don't exist directly, check in settings
      else if (initialGraph.settings?.edges && initialGraph.settings.edges.length > 0) {
        setEdges(initialGraph.settings.edges);
      }
      
      // If bgColor exists, load it
      if (initialGraph.bgColor) {
        setBgColor(initialGraph.bgColor);
      } else if (initialGraph.settings?.bgColor) {
        setBgColor(initialGraph.settings.bgColor);
      }
    }
  }, [initialGraph, setNodes, setEdges, setBgColor]);

  // Use graph interactivity hook
  const {
    isInteractive,
    setIsInteractive,
    isPanEnabled,
    setIsPanEnabled,
    zoomIn,
    zoomOut,
    fitView,
    toggleInteractivity,
    togglePan,
  } = useGraphInteractivity(reactFlowInstanceRef);

  // Create the context value object
  const contextValue = useMemo<GraphContextType>(
    () => ({
      setNodes,
      setEdges,
      nodes,
      edges,
      selectedNodeId,
      setSelectedNodeId,
      bgColor,
      setBgColor,
      onNodesChange,
      onEdgesChange,
      zoomIn,
      zoomOut,
      fitView,
      toggleInteractivity,
      isInteractive,
      setIsInteractive,
      isPanEnabled,
      setIsPanEnabled,
      togglePan,
      reactFlowInstanceRef,
      defaultViewport,
      backgroundVariant,
      messages,
      setMessages,
      currentNodeId,
      setCurrentNodeId,
      graphId,
      graphTitle,
      setGraphTitle,
      sortByDepth,
      setSortByDepth,
      toggleSortMode,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      nodes,
      edges,
      bgColor,
      setBgColor,
      onNodesChange,
      onEdgesChange,
      zoomIn,
      zoomOut,
      fitView,
      toggleInteractivity,
      isInteractive,
      setIsInteractive,
      isPanEnabled,
      setIsPanEnabled,
      togglePan,
      reactFlowInstanceRef,
      messages,
      setMessages,
      currentNodeId,
      setCurrentNodeId,
      selectedNodeId,
      setSelectedNodeId,
      graphId,
      graphTitle,
      setGraphTitle,
      sortByDepth,
      setSortByDepth,
      toggleSortMode,
    ],
  );

  return (
    <GraphContext.Provider value={contextValue}>
      <ReactFlowProvider>
        <GraphDimensionsProvider>
          <GraphHistoryProvider>{children}</GraphHistoryProvider>
        </GraphDimensionsProvider>
      </ReactFlowProvider>
    </GraphContext.Provider>
  );
};
