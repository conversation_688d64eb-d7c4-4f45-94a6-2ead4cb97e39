"use client";

import React from "react";
import { useGraph } from "@/contexts/GraphContext";
import { useNodeTags } from "@/hooks/useNodeTags";
import { LiaThumbtackSolid } from "react-icons/lia";
import { FaRegThumbsUp, FaRegThumbsDown, FaRegLightbulb } from "react-icons/fa";
import { VscError, VscInfo } from "react-icons/vsc";
import { BiSolidShow, BiSolidHide } from "react-icons/bi";
import { BsFire } from "react-icons/bs";
import { NodeTag } from "@/utils/nodeBuilder";
import { VerticalDivider } from "@/components/ui/VerticalDivider";

/**
 * Component for tag controls
 * Shows all available tags in a single line
 * Only visible when a node is selected
 */
export const TagControls: React.FC = () => {
  const { selectedNodeId } = useGraph();
  const { getNodeTags, toggleNodeTag, getDefaultTags } = useNodeTags();

  // Only show the tag controls if a node is selected
  if (!selectedNodeId) {
    return null;
  }

  const nodeTags = getNodeTags(selectedNodeId);
  const defaultTags = getDefaultTags();

  // Check if a tag is active on the node
  const isTagActive = (tagId: string) => {
    return nodeTags.some((t) => t.id === tagId);
  };

  // Handle tag click to toggle it on the selected node
  const handleTagClick = (tag: NodeTag) => {
    if (selectedNodeId) {
      toggleNodeTag(selectedNodeId, tag);
    }
  };

  // Get icon component based on icon name
  const getIconComponent = (iconName: string, color?: string) => {
    const iconProps = {
      size: 18,
      color: color || "currentColor",
      className: "inline-block",
    };

    switch (iconName) {
      case "LiaThumbtackSolid":
        return <LiaThumbtackSolid {...iconProps} />;
      case "FaRegThumbsUp":
        return <FaRegThumbsUp {...iconProps} />;
      case "FaRegThumbsDown":
        return <FaRegThumbsDown {...iconProps} />;
      case "FaRegLightbulb":
        return <FaRegLightbulb {...iconProps} />;
      case "VscError":
        return <VscError {...iconProps} />;
      case "VscInfo":
        return <VscInfo {...iconProps} />;
      case "BiSolidShow":
        return <BiSolidShow {...iconProps} />;
      case "BiSolidHide":
        return <BiSolidHide {...iconProps} />;
      case "BsFire":
        return <BsFire {...iconProps} />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex items-center space-x-2">
        {defaultTags.map((tag) => (
          <button
            key={tag.id}
            className={`inline-flex items-center justify-center p-1.5 rounded-full text-xs font-medium cursor-pointer hover:opacity-90 transition-opacity shadow-sm ${
              isTagActive(tag.id)
                ? "ring-1 ring-white ring-opacity-70"
                : "opacity-80"
            }`}
            style={{
              backgroundColor: isTagActive(tag.id)
                ? tag.color || "#3b82f6"
                : "rgba(255,255,255,0.2)",
              color: "white",
            }}
            onClick={() => handleTagClick(tag)}
            title={`${isTagActive(tag.id) ? "Remove" : "Add"} ${tag.label || tag.id} tag`}
          >
            {getIconComponent(
              tag.icon,
              isTagActive(tag.id) ? "white" : tag.color
            )}
          </button>
        ))}
      </div>
      {/* Vertical Divider */}
      <VerticalDivider margin="mr-2" />
    </>
  );
};

export default TagControls;
