/* eslint-disable no-console */

import { NextResponse } from "next/server";
import {getSupabaseToken } from "@/lib/auth/clerk";

export const dynamic = 'force-dynamic';
/**
 * GET /api/get-supabase-token
 * Returns JWT token from Supabase for the current session
 *
 */
export async function GET() {
  try {
    
    // Get token using our auth library function
    const tokenResult = await getSupabaseToken();
    
    if (tokenResult.error) {
      console.error("[API][get-supabase-token] Error getting token:", tokenResult.error);
      
      if (tokenResult.error.code === "auth/no-session") {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      
      return NextResponse.json(
        { error: tokenResult.error.message },
        { status: 500 }
      );
    }
    
    if (!tokenResult.data) {
      console.error("[API][get-supabase-token] No token returned from <PERSON>");
      return NextResponse.json(
        { error: "Failed to retrieve token" },
        { status: 500 }
      );
    }
    
    // Return token to client
    return NextResponse.json({ token: tokenResult.data }, { status: 200 });
  } catch (error) {
    console.error("[API][get-supabase-token] Unhandled error:", error);
    
    // Determine error type
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    // Check for session-related errors
    if (errorMessage.includes("No active session") || errorMessage.includes("Unauthorized")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: `Error getting token: ${errorMessage}` },
      { status: 500 }
    );
  }
}