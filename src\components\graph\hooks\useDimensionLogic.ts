/* eslint-disable react-hooks/exhaustive-deps */
import React from 'react';
import { useGraphDimensions } from "@/contexts/GraphDimensionsContext";
import { useEffect } from "react";

export default function useDimensionLogic(
  containerRef: React.RefObject<HTMLDivElement>,
) {
  const { setGraphDimensions } = useGraphDimensions();

  // Measure container dimensions and update context
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setGraphDimensions(width, height);
      }
    };

    // Initial measurement
    updateDimensions();

    // Set up resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    const currentRef = containerRef.current; // Store ref in variable for cleanup

    if (currentRef) {
      resizeObserver.observe(currentRef);
    }

    // Clean up
    return () => {
      if (currentRef) {
        resizeObserver.unobserve(currentRef);
      }
      resizeObserver.disconnect();
    };
  }, [setGraphDimensions, containerRef.current]);
}
