import { Panel, Node } from "@xyflow/react";
import { type Message } from "@ai-sdk/react";
import { NodeTag } from "@/utils/nodeBuilder";
import { LiaThumbtackSolid } from 'react-icons/lia';
import { FaRegThumbsUp, FaRegThumbsDown, FaRegLightbulb } from 'react-icons/fa';
import { VscError, VscInfo } from 'react-icons/vsc';
import { BiSolidShow, BiSolidHide } from 'react-icons/bi';
import { BsFire } from 'react-icons/bs';

export function PreviewSelected({
  selectedNodeId,
  nodes,
}: {
  selectedNodeId: string;
  nodes: Node[];
}) {
  // Get the selected node
  const selectedNode = nodes.find((n) => n.id === selectedNodeId);
  
  // Get tags from the node data
  const nodeTags = selectedNode?.data?.tags as NodeTag[] || [];
  
  // Helper function to render tag icons
  const getIconComponent = (iconName: string, color?: string) => {
    const iconProps = { 
      size: 14, 
      color: color || 'currentColor'
    };
    
    switch (iconName) {
      case 'LiaThumbtackSolid': return <LiaThumbtackSolid {...iconProps} />;
      case 'FaRegThumbsUp': return <FaRegThumbsUp {...iconProps} />;
      case 'FaRegThumbsDown': return <FaRegThumbsDown {...iconProps} />;
      case 'FaRegLightbulb': return <FaRegLightbulb {...iconProps} />;
      case 'VscError': return <VscError {...iconProps} />;
      case 'VscInfo': return <VscInfo {...iconProps} />;
      case 'BiSolidShow': return <BiSolidShow {...iconProps} />;
      case 'BiSolidHide': return <BiSolidHide {...iconProps} />;
      case 'BsFire': return <BsFire {...iconProps} />;
      default: return null;
    }
  };
  
  return (
    <Panel
      position="top-left"
      className="bg-gray-800 p-2 rounded shadow border border-gray-700"
      style={{
        maxWidth: "25%",
        maxHeight: "25vh",
        overflow: "hidden",
        width: "auto",
      }}
    >
      <div className="text-sm text-white">
        <p className="mb-1"><span className="font-bold">Selected Node:</span> {selectedNodeId}</p>
        <p className="mb-1"><span className="font-bold">Label:</span> {(selectedNode?.data?.label as string) || "No label"}</p>
        <p className="mb-1"><span className="font-bold">Type:</span> {selectedNode?.type || "default"}</p>
        
        {/* Display node tags */}
        <p className="mb-1">
          <span className="font-bold">Tags:</span>{' '}
          {nodeTags.length > 0 ? (
            <span className="flex items-center gap-1 mt-1">
              {nodeTags.map((tag) => (
                <span
                  key={tag.id}
                  className="inline-flex items-center justify-center p-1 rounded-full text-xs"
                  style={{ 
                    backgroundColor: 'rgb(233, 241, 255)',
                    width: '18px',
                    height: '18px',
                  }}
                  title={tag.label || tag.id}
                >
                  {getIconComponent(tag.icon, tag.color)}
                </span>
              ))}
            </span>
          ) : (
            <span className="italic text-gray-400">No tags</span>
          )}
        </p>
        
        <p className="mb-1"><span className="font-bold">User:</span>
          <br />
          <span className="pl-2 inline-block italic">
            {`${(selectedNode?.data?.user as Message)
              ?.content?.slice(0, 30) || "No user dialog"}...`}
          </span>
        </p>
        <p className="mb-1"><span className="font-bold">Assistant:</span>
          <br />
          <span className="pl-2 inline-block italic">
            {`${(selectedNode?.data
              ?.assistant as Message
            )?.content?.slice(0, 30) || "No assistant dialog"}...`}
          </span>
        </p>
      </div>
    </Panel>
  );
}
