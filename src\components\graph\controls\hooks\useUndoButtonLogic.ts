import React, { useMemo } from "react";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

export const useUndoButtonLogic = () => {
  const { undo, canUndo } = useGraphHistory();
  
  // Add keydown event listener for undo shortcut (Ctrl+Z)
  const undoFn = React.useCallback((event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
      event.preventDefault(); // Prevent browser's default behavior
      undo();
    }
  }, [undo]);
  
  // Set up event listener
  React.useEffect(() => {
    document.addEventListener("keydown", undoFn);
    return () => {
      document.removeEventListener("keydown", undoFn);
    };
  }, [undoFn]);

  return useMemo(() => ({
    undo,
    canUndo,
    title: "Undo"
  }), [undo, canUndo]);
};