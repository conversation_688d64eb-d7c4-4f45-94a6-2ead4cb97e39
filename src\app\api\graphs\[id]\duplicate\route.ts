/* eslint-disable no-console */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { RepositoryFactory } from "@/repositories";
import { CreateGraphDTO } from "@/types/dto";

/**
 * Duplicate a graph by ID
 * POST /api/graphs/:id/duplicate
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get graph ID from URL parameters
    const graphId = params.id;

    // Check user authentication
    const authObject = await auth();
    const { userId: clerkUserId, sessionId } = authObject;

    if (!clerkUserId) {
      console.error(`[API][graphs/id/duplicate] User not authenticated`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the session ID from either auth or request headers
    const headerSessionId = request.headers.get("x-clerk-session-id");
    const effectiveSessionId = sessionId || headerSessionId;
    // Set the session ID for JWT refresh if needed
    if (effectiveSessionId) {
      RepositoryFactory.setSessionId(effectiveSessionId);
    }

    // Get repositories
    const graphRepository = RepositoryFactory.getGraphRepository();
    const nodeRepository = RepositoryFactory.getNodeRepository();
    const edgeRepository = RepositoryFactory.getEdgeRepository();
    const userRepository = RepositoryFactory.getUserRepository();
    // Fetch the original graph
    const graph = await graphRepository.findById(graphId);

    if (!graph) {
      console.error(`[API][graphs/id/duplicate] Graph with ID ${graphId} not found`);
      return NextResponse.json({ error: "Graph not found" }, { status: 404 });
    }
// Find the user by Clerk ID
const user = await userRepository.findByClerkId(clerkUserId);

// Check if the graph belongs to the user
if (!user || graph.userId !== user.id) {
  // If this is a server-side request, we don't need to check ownership
  if (typeof window === 'undefined') {
    return NextResponse.json(graph);
  }
  
  console.error(`[API][graphs/id/duplicate] Unauthorized: Graph (${graph.userId}) does not belong to user (${user?.id})`);
  return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
}
    // Create new graph based on the original
    const newGraphData: CreateGraphDTO = {
      title: `${graph.title} (copy)`,
      description: graph.description || undefined,
      userId: user.id,
      bgColor: graph.bgColor,
      settings: graph.settings,
    };
    // Save the new graph to the database
    const createdGraph = await graphRepository.create(newGraphData);

    // Fetch nodes for the original graph and duplicate them
    const originalNodes = await nodeRepository.findByGraphId(graphId);
    if (originalNodes && originalNodes.length > 0) {
      // Create new nodes for the duplicated graph
      const nodePromises = originalNodes.map((node) => {
        return nodeRepository.create({
          label: node.label,
          x: node.x,
          y: node.y,
          type: node.type,
          userDialog: node.userDialog,
          assistantDialog: node.assistantDialog,
          tags: node.tags,
          isIncluded: node.isIncluded,
          data: node.data,
          graphId: createdGraph.id,
        });
      });

      await Promise.all(nodePromises);
    }

    // Fetch edges for the original graph and duplicate them
    const originalEdges = await edgeRepository.findByGraphId(graphId);
    if (originalEdges && originalEdges.length > 0) {
      // Create new edges for the duplicated graph
      const edgePromises = originalEdges.map((edge) => {
        return edgeRepository.create({
          label: edge.label || undefined,
          type: edge.type,
          sourceId: edge.sourceId,
          targetId: edge.targetId,
          isBlocked: edge.isBlocked,
          tagFilter: edge.tagFilter,
          data: edge.data,
          graphId: createdGraph.id,
        });
      });

      await Promise.all(edgePromises);
    }

    // Return the created graph
    return NextResponse.json({
      id: createdGraph.id,
      title: createdGraph.title,
      description: createdGraph.description,
      userId: createdGraph.userId,
      createdAt: createdGraph.createdAt,
      updatedAt: createdGraph.updatedAt,
      settings: createdGraph.settings,
      bgColor: createdGraph.bgColor,
      nodes: [],
      edges: [],
    });
  } catch (error) {
    console.error("Error duplicating graph:", error);

    return NextResponse.json(
      { error: "Failed to duplicate graph" },
      { status: 500 }
    );
  }
}
