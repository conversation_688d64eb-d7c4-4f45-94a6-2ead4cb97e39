import React from 'react';
import type { Metada<PERSON> } from "next";
import { AppProviders } from "@/components/providers/AppProviders";
import { ThemeProviderCustom } from "@/components/providers/ThemeProviderCustom";

import "./globals.css";

export const metadata: Metadata = {
  title: "GraphChat",
  description: "Chat with your graph data",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}): JSX.Element {
  return (
    <html lang="en" className="dark">
      <body className="antialiased">
        <ThemeProviderCustom>
          <AppProviders>{children}</AppProviders>
        </ThemeProviderCustom>
      </body>
    </html>
  );
}
