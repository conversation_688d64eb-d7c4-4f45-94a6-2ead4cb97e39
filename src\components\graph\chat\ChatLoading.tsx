import React from "react";

interface ChatLoadingProps {
  /**
   * Optional custom message to display during loading
   */
  message?: string;
}

/**
 * Loading component for chat messages
 */
export const ChatLoading: React.FC<ChatLoadingProps> = ({
  message = "Thinking...",
}) => {
  return (
    <div className="p-4 rounded-xl bg-gray-900 mr-auto max-w-[80%]">
      <p className="text-sm font-semibold mb-1 text-blue-400">AI Assistant</p>
      <p className="text-sm text-white">{message}</p>
    </div>
  );
};
