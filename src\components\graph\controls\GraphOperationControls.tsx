
import React from "react";
import TagControls from "./TagControls";
import { DeleteButton } from "./DeleteButton";
import { AddNodeButton } from "./AddNodeButton";
import { VerticalDivider } from "@/components/ui/VerticalDivider";
import { ResetButton } from "./ResetButton";
import { SortModeButton } from "./SortModeButton";

/**
 * Component for graph operation controls (add node, delete node, clear graph, reset graph)
 */
export const GraphOperationControls: React.FC = () => {
  return (
    <>
      <div className="flex space-x-2 items-center">
        {/* Main graph operations */}
        <AddNodeButton />
        <DeleteButton />
        <ResetButton />
        <SortModeButton />
      </div>
      <VerticalDivider margin="mx-2" />
      <div className="flex space-x-2 items-center">
        <TagControls />
      </div>
    </>
  );
};

