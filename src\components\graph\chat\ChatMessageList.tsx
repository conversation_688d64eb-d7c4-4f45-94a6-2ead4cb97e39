"use client";

import React, { useEffect, useState, useRef } from "react";
import { type Message } from "@ai-sdk/react";
import { AutoScroll } from "./AutoScroll";
import { ChatLoading } from "./ChatLoading";
import { Markdown } from "./markdown/MarkDown";

interface ChatMessageListProps {
  /**
   * List of chat messages to display
   */
  messages: Message[];

  /**
   * Whether the chat is currently loading
   */
  isLoading: boolean;
}

/**
 * Component that displays a list of chat messages with loading indicators
 */
export const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  isLoading,
}) => {
  // Track the current assistant message being streamed
  const [currentAssistantMessage, setCurrentAssistantMessage] =
    useState<Message | null>(null);
  
  // Track when messages change to trigger auto-scroll
  const [shouldAutoScroll, setShouldAutoScroll] = useState(false);
  const messagesLengthRef = useRef(messages.length);
  const lastMessageContentRef = useRef<string>("");
  
  // Update the current assistant message when streaming starts
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === "assistant") {
        setCurrentAssistantMessage(lastMessage);
      }
    }
    
    // Check if messages were added or content changed
    if (messages.length !== messagesLengthRef.current) {
      // New message was added
      messagesLengthRef.current = messages.length;
      setShouldAutoScroll(true);
    } else if (messages.length > 0) {
      // Check if the content of the last message changed (streaming)
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.content !== lastMessageContentRef.current) {
        lastMessageContentRef.current = lastMessage.content;
        setShouldAutoScroll(true);
      }
    }
    
    // Reset auto-scroll trigger after a short delay
    if (shouldAutoScroll) {
      const timer = setTimeout(() => {
        setShouldAutoScroll(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [messages, shouldAutoScroll]);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages
        .filter((message) => message.role !== "system")
        .map((message) => (
          <div
            key={message.id}
            className={`p-4 rounded-xl ${
              message.role === "user"
                ? "bg-gray-800 ml-auto"
                : "bg-gray-900 mr-auto"
            } max-w-[80%]`}
          >
            <p className="text-sm font-semibold mb-1 text-blue-400">
              {message.role === "user" ? "You" : "AI Assistant"}
            </p>
            <Markdown>{message.content}</Markdown>

            {/* Streaming indicator for assistant messages */}
            {isLoading &&
              message.role === "assistant" &&
              message === currentAssistantMessage && (
                <span className="inline-block h-2 w-2 bg-blue-500 rounded-full animate-pulse ml-1"></span>
              )}
          </div>
        ))}

      {/* Loading indicator if no message yet */}
      {isLoading && !currentAssistantMessage && <ChatLoading />}

      {/* Auto-scroll component with improved behavior */}
      <AutoScroll 
        shouldScroll={shouldAutoScroll || isLoading} 
        threshold={150}
      />
    </div>
  );
};
