// Типы для Supabase
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          clerk_id: string;
          email: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          clerk_id: string;
          email?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          clerk_id?: string;
          email?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      chat_graphs: {
        Row: {
          id: string;
          title: string;
          user_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title?: string;
          user_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chat_graphs_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_nodes: {
        Row: {
          id: string;
          graph_id: string;
          user_dialog: string;
          assistant_dialog: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          graph_id: string;
          user_dialog: string;
          assistant_dialog: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          graph_id?: string;
          user_dialog?: string;
          assistant_dialog?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chat_nodes_graph_id_fkey";
            columns: ["graph_id"];
            referencedRelation: "chat_graphs";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_node_relationships: {
        Row: {
          id: string;
          parent_id: string | null;
          child_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          parent_id?: string | null;
          child_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          parent_id?: string | null;
          child_id?: string | null;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chat_node_relationships_child_id_fkey";
            columns: ["child_id"];
            referencedRelation: "chat_nodes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chat_node_relationships_parent_id_fkey";
            columns: ["parent_id"];
            referencedRelation: "chat_nodes";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_node_metadata: {
        Row: {
          node_id: string;
          tags: string[] | null;
          embeddings: number[] | null;
          additional_data: Json | null;
        };
        Insert: {
          node_id: string;
          tags?: string[] | null;
          embeddings?: number[] | null;
          additional_data?: Json | null;
        };
        Update: {
          node_id?: string;
          tags?: string[] | null;
          embeddings?: number[] | null;
          additional_data?: Json | null;
        };
        Relationships: [
          {
            foreignKeyName: "chat_node_metadata_node_id_fkey";
            columns: ["node_id"];
            referencedRelation: "chat_nodes";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: Record<string, unknown>;
    Functions: Record<string, unknown>;
    Enums: Record<string, unknown>;
    CompositeTypes: Record<string, unknown>;
  };
}