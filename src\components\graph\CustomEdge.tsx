import React from 'react';
import { 
  BaseEdge, 
  EdgeProps, 
  getBezierPath, 
  EdgeLabelRenderer
} from '@xyflow/react';
import { FaTag } from 'react-icons/fa';
import { NODE_TAGS } from '@/lib/constants';

// Define EdgeData type
interface EdgeData {
  tagFilter?: string[];
  isBlocked?: boolean;
}

// Компонент для отображения тегов на ребре
const EdgeTags: React.FC<{ tagIds: string[], isBlocked: boolean }> = ({ tagIds, isBlocked }) => {
  if (!tagIds || tagIds.length === 0) return null;
  
  return (
    <div 
      className={`flex items-center gap-1 px-2 py-1 rounded-md bg-white shadow-md border ${isBlocked ? 'border-red-500' : 'border-gray-200'}`}
    >
      {tagIds.map((tagId) => {
        const tag = NODE_TAGS.DEFAULTS.find((t) => t.id === tagId);
        
        if (!tag) return null;
        
        return (
          <div 
            key={tagId}
            className="flex items-center gap-1 text-xs text-gray-700"
            title={tag.label}
          >
            <FaTag className="text-xs" style={{ color: tag.color }} />
            <span>{tag.label}</span>
          </div>
        );
      })}
    </div>
  );
};

// Кастомный компонент для ребра
const CustomEdge: React.FC<EdgeProps> = ({ 
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  data
}) => {
  // Получаем путь ребра (кривая Безье)
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // Проверяем, есть ли фильтр тегов и блокировка
  const edgeData = (data || {}) as EdgeData;
  const tagFilter = edgeData.tagFilter || [];
  const isBlocked = edgeData.isBlocked || false;

  return (
    <>
      {/* Draw base edge */}
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      
      {/* Render labels only if tags exist */}
      {tagFilter.length > 0 && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              pointerEvents: 'all',
              zIndex: 10,
            }}
            className="nodrag nopan"
          >
            <EdgeTags tagIds={tagFilter} isBlocked={isBlocked} />
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

export default CustomEdge; 