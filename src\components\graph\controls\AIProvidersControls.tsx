"use client";

import React, { useEffect } from "react";
import { useAtom } from "jotai";
import {Providers, PROVIDERS_MAP} from "@/lib/constants"
import { aiProviders } from "@/lib/ai-provider";
import { Provider<PERSON>trom, Model<PERSON>tom } from "@/contexts/atoms";

export const AIProvidersControls = () => {
  const [provider, setProvider] = useAtom(ProviderAtrom);
  const [model, setModel] = useAtom(ModelAtom);

  // Обновляем модель при изменении провайдера
  useEffect(() => {
    // Устанавливаем модель по умолчанию для выбранного провайдера
    setModel(PROVIDERS_MAP[provider as keyof typeof PROVIDERS_MAP].DEFAULT as string);
  }, [provider, setModel]);

  // Получаем доступные модели для текущего провайдера
  const getModelsForProvider = () => {
    if (!provider) return [];
    
    // Получаем все значения моделей для выбранного провайдера
    const providerKey = provider as keyof typeof aiProviders;
    return Array.from(new Set(Object.values(aiProviders[providerKey].models)));
  };

  return (
    <div className="flex items-center">
    <select 
      className="bg-gray-800 text-gray-200 text-sm rounded-md border border-gray-700 px-2 py-1 mr-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
      onChange={(e) => {
        setProvider(e.target.value);
      }}
      value={provider}
    >
      {Object.entries(Providers).map(([key, value]) => (
        <option key={key} value={value}>
          {key.charAt(0).toUpperCase() + key.slice(1).toLowerCase()}
        </option>
      ))}
    </select>

    <select
      className="bg-gray-800 text-gray-200 text-sm rounded-md w-48 border border-gray-700 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
      onChange={(e) => {
        setModel(e.target.value);
      }}
      value={model}
    >
      {getModelsForProvider()
          .map((modelValue) => (
        <option key={modelValue} value={modelValue}>
          {modelValue}
        </option>
      ))}
    </select>
  </div>
  );
}