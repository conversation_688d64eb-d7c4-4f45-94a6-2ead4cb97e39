import { useCallback } from "react";
import { Edge } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

/**
 * Custom hook to delete connections between nodes
 * Provides functions to delete a single edge or all edges connected to a node
 */
export const useDeleteConnection = () => {
  const {
    nodes,
    edges,
    setEdges,
    selectedNodeId,
    currentNodeId,
    bgColor,
    messages,
  } = useGraph();
  
  const { saveToHistory } = useGraphHistory();

  /**
   * Delete a single connection by its ID
   * @param edgeId - The ID of the edge to delete
   */
  const deleteConnection = useCallback(
    (edgeId: string) => {
      // Save current state to history before deleting the edge
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Remove the edge with the specified ID
      setEdges((eds: Edge[]) => eds.filter((edge) => edge.id !== edgeId));
    },
    [
      nodes,
      edges,
      setEdges,
      saveToHistory,
      bgColor,
      selectedNodeId,
      currentNodeId,
      messages,
    ]
  );

  /**
   * Delete all connections related to a specific node
   * @param nodeId - The ID of the node whose connections should be deleted
   */
  const deleteNodeConnections = useCallback(
    (nodeId: string) => {
      // Save current state to history before deleting the edges
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Remove all edges where the specified node is either source or target
      setEdges((eds: Edge[]) =>
        eds.filter(
          (edge) => edge.source !== nodeId && edge.target !== nodeId
        )
      );
    },
    [
      nodes,
      edges,
      setEdges,
      saveToHistory,
      bgColor,
      selectedNodeId,
      currentNodeId,
      messages,
    ]
  );

  /**
   * Delete a connection between two specific nodes
   * @param sourceId - The ID of the source node
   * @param targetId - The ID of the target node
   */
  const deleteConnectionBetweenNodes = useCallback(
    (sourceId: string, targetId: string) => {
      // Save current state to history before deleting the edge
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Remove edges connecting the specified source and target nodes
      setEdges((eds: Edge[]) =>
        eds.filter(
          (edge) => !(edge.source === sourceId && edge.target === targetId)
        )
      );
    },
    [
      nodes,
      edges,
      setEdges,
      saveToHistory,
      bgColor,
      selectedNodeId,
      currentNodeId,
      messages,
    ]
  );

  return {
    deleteConnection,
    deleteNodeConnections,
    deleteConnectionBetweenNodes,
  };
};
