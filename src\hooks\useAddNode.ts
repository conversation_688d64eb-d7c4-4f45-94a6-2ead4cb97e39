/* eslint-disable no-console */

import { useCallback } from "react";
import { type Node, type Edge } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphDimensions } from "@/contexts/GraphDimensionsContext"; // Import the new context
import { NodeBuilder, type NodeData } from "@/utils/nodeBuilder";
import { EdgeBuilder } from "@/utils/edgeBuilder";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";
import { useCurrentNode } from "./useCurrentNode";
import { type Message } from "postcss";
import { nanoid } from "nanoid";

/**
 * Hook to add a new node to the graph
 * @returns Function that accepts parameters to add a new node
 */
export const useAddNode = () => {
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    bgColor,
    selectedNodeId,
    setCurrentNodeId,
    setSelectedNodeId,
    currentNodeId,
    messages,
  } = useGraph();

  // Get dimensions from GraphDimensionsContext instead of GraphContext
  const { graphWidth, graphHeight } = useGraphDimensions();
  const { saveToHistory } = useGraphHistory();

  // Get the current node
  const { currentNode } = useCurrentNode();

  return useCallback(
    (args?: { data?: NodeData; isAssistant?: boolean }) => {
      const { data, isAssistant } = args || {};
      const newNodeId = nanoid();
      // Check if we have valid dimensions
      if (graphWidth <= 0 || graphHeight <= 0) {
        console.warn("Cannot add node: Invalid graph dimensions");
        return;
      }

      // Default node dimensions (approximate)
      const nodeWidth = 180;
      const nodeHeight = 40;
      const padding = 100; // Define padding from edges (in pixels)
      const verticalSpacing = nodeHeight; // Vertical spacing between nodes

      let position: { x: number; y: number };

      // --- Change Initial Placement ---
      // Place initial node more centrally, respecting padding
      const initialX = Math.max(padding, graphWidth / 2 - nodeWidth / 2); // Uses nodeWidth
      const initialY = 50;
      // --- End Change ---

      if (nodes.length === 0) {
        // First node centered horizontally near the top
        position = { x: initialX, y: initialY };
      } else if (currentNode && currentNode.position) {
        // Position the new node below the current node, alternating left/right

        // Calculate child count directly from current edges state *before* adding the new one
        const existingChildEdges = edges.filter(
          (edge) => edge.source === currentNode.id
        );
        const existingChildCount = existingChildEdges.length;

        // Calculate the child count *after* adding this new node
        const newChildCount = existingChildCount + 1;

        // Determine horizontal offset based on the *new* number of children
        let horizontalOffset = 0;
        // Start offsetting horizontally from the second child onwards
        if (newChildCount > 1) {
          // Alternate offset left (odd new count) and right (even new count)
          const sign = newChildCount % 2 === 0 ? 1 : -1;
          // Calculate the index of the pair (1st pair, 2nd pair, etc.)
          const pairIndex = Math.ceil((newChildCount - 1) / 2);
          // Calculate the magnitude of the offset based on node width and pair index
          // Moderate spacing: Use full node width plus a small gap (10px)
          horizontalOffset = sign * (nodeWidth + 10) * pairIndex;
        }

        position = {
          x: currentNode.position.x + horizontalOffset, // Apply horizontal offset
          y: currentNode.position.y + nodeHeight + verticalSpacing, // Below current node with spacing
        };
        // If the new position would be below the visible area, reset to the top
        if (position.y + nodeHeight + padding > graphHeight) {
          // Start a new column to the right
          position = {
            // Place the new column relative to the *parent's* x
            x: currentNode.position.x + nodeWidth + 20, // Move to the right of the parent
            y: initialY, // Reset to the initial Y
          };
        }

        // Make sure the node is within the visible area (respecting container coordinates)
        // Keep the clamping logic for now, new initial position might prevent issues
        position.x = Math.min(
          Math.max(position.x, padding),
          graphWidth - padding - nodeWidth
        );
        position.y = Math.min(
          Math.max(position.y, padding),
          graphHeight - padding - nodeHeight
        );
      } else {
        // If no current node, but nodes exist (should not happen with current logic, but handle defensively)
        position = { x: initialX, y: initialY };
      }

      // Create the new node object using NodeBuilder
      let newNode: Node;
      if (nodes.length === 0) {
        // Create an input node for the first node
        newNode = NodeBuilder.createInputNode(
          newNodeId,
          position,
          data?.user
            ? `${(data?.user as Message)?.content?.slice(0, 30)}...`
            : "Empty node",
          data
        );
      } else {
        // Create a regular node for subsequent nodes
        newNode = NodeBuilder.createNodeAtPosition(
          newNodeId,
          position,
          data?.user
            ? `${(data?.user as Message)?.content?.slice(0, 30)}...`
            : "Empty node",
          data
        );
      }

      // Ensure the node type is set to 'custom'
      newNode.type = 'custom';

      // Save current state to history before adding the node
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages: isAssistant && messages.length > 1 ? messages.slice(0, -2) : messages,
      });

      // Add the new node to the graph
      setNodes((nds: Node[]) => [...nds, newNode]);

      // If there's a current node, create a connection between the current node and the new node
      if (currentNode) {
        // When adding a new node, there won't be an existing edge with the same source and target
        // So we don't need to check for existing blocked edges here
        
        // Create a new edge
        const newEdge: Edge = EdgeBuilder.createCustomEdge(
          {
            id: nanoid(),
            source: currentNode.id,
            target: newNodeId,
          },
          {
            // New edges are never blocked by default
            isBlocked: false
          }
        );

        // Add the new edge to the graph
        setEdges((eds: Edge[]) => [...eds, newEdge]);
      }

      // Set this new node as the current node and clear selected node
      setCurrentNodeId(newNodeId);
      setSelectedNodeId(null);
    },
    [
      nodes,
      edges,
      setNodes,
      setEdges,
      bgColor,
      graphWidth,
      graphHeight,
      currentNode,
      saveToHistory,
      selectedNodeId,
      setCurrentNodeId,
      setSelectedNodeId,
      currentNodeId,
      messages,
    ]
  );
};

export default useAddNode;
