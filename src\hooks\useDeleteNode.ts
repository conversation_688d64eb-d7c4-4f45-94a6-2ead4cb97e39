import { useCallback } from "react";
import { Node, <PERSON> } from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";
import { type Message } from "@ai-sdk/react";

/**
 * Custom hook to delete the selected node from the graph
 * @returns Function to delete the selected node
 */
export const useDeleteNode = () => {
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    currentNodeId,
    messages,
    setMessages,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();

  /**
   * Deletes the selected node from the graph
   * @param nodeIdToDelete - Optional node ID to delete. If not provided, uses selectedNodeId from context
   */
  return useCallback((nodeIdToDelete?: string) => {
    const nodeId = nodeIdToDelete || selectedNodeId;
    if (!nodeId) return;

    // Save current state to history before deleting the node
    saveToHistory({
      nodes: [...nodes],
      edges: [...edges],
      selectedNodeId,
      bgColor,
      currentNodeId,
      messages,
    });
    const node = nodes.find((node: Node) => node.id === nodeId)!;
    const { data } = node;
    const { user, assistant } = data;
    if (user || assistant) {
      setMessages((msgs: Message[]) =>
        msgs.filter(
          (msg: Message) =>
            msg.id !== (user as Message)?.id &&
            msg.id !== (assistant as Message)?.id
        )
      );
    }
    // Filter out the selected node and its connected edges
    setNodes((nds: Node[]) =>
      nds.filter((node: Node) => node.id !== nodeId)
    );
    setEdges((eds: Edge[]) =>
      eds.filter(
        (edge: Edge) =>
          edge.source !== nodeId && edge.target !== nodeId
      )
    );

    // Reset the selected node ID and current node ID in the context
    if (nodeId === selectedNodeId) {
      setSelectedNodeId(null);
    }
    if (nodeId === currentNodeId) {
      setCurrentNodeId(null);
    }
  }, [
    nodes,
    edges,
    setNodes,
    setEdges,
    saveToHistory,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    currentNodeId,
    messages,
    setMessages,
  ]);
};
