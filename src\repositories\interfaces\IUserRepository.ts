import { UserDTO as User, <PERSON>reateUserDTO, UpdateUserDTO } from "@/types/dto";

/**
 * Interface for User repository
 * Defines methods for interacting with user data
 */
export interface IUserRepository {
  /**
   * Find a user by ID
   * @param id - The user ID
   * @returns The user or null if not found
   */
  findById(id: string): Promise<User | null>;

  /**
   * Find a user by Clerk ID
   * @param clerkId - The Clerk user ID
   * @returns The user or null if not found
   */
  findByClerkId(clerkId: string): Promise<User | null>;

  /**
   * Find a user by email
   * @param email - The user's email
   * @returns The user or null if not found
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Create a new user
   * @param data - The user data
   * @returns The created user
   */
  create(data: CreateUserDTO): Promise<User>;

  /**
   * Update an existing user
   * @param id - The user ID
   * @param data - The updated user data
   * @returns The updated user
   */
  update(id: string, data: UpdateUserDTO): Promise<User>;

  /**
   * Delete a user
   * @param id - The user ID
   * @returns True if successful, false otherwise
   */
  delete(id: string): Promise<boolean>;

  /**
   * Find all users
   * @returns Array of all users
   */
  findAll(): Promise<User[]>;
}
