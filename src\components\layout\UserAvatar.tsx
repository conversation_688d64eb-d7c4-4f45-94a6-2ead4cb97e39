"use client";

import { useUser, SignOutButton } from "@clerk/nextjs";
import { useState } from "react";

export function UserAvatar(): JSX.Element {
  const { user, isLoaded } = useUser();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

 if (!isLoaded) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 rounded-full bg-gray-700 animate-pulse"></div>
        <div className="h-4 w-52 bg-gray-700 rounded animate-pulse"></div>
      </div>
    );
  }

  const avatarLetter =
    user?.firstName?.[0]?.toUpperCase() ||
    (user?.emailAddresses?.[0]?.emailAddress?.[0]?.toUpperCase() || "");
  const userEmail = user?.emailAddresses?.[0]?.emailAddress || "";

  return (
    <div className="relative">
      <button 
        className="flex items-center gap-2 focus:outline-none"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white">
          {avatarLetter}
        </div>
        <span className="text-white">{userEmail}</span>
      </button>
      
      {isMenuOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-gray-800 rounded-md shadow-lg py-1 z-10">
          <div className="px-4 py-2 text-sm text-gray-300 border-b border-gray-700">
            Signed in as<br />
            <span className="font-medium text-white">{userEmail}</span>
          </div>
        
          <div className="border-t border-gray-700 mt-1">
            <SignOutButton>
              <button className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                Sign Out
              </button>
            </SignOutButton>
          </div>
        </div>
      )}
    </div>
  );
}
