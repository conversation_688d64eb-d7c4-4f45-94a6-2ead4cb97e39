/**
 * Clerk Authentication Utilities (Server-side)
 * 
 * This module provides server-side functions for working with Clerk authentication.
 */
import { auth, clerkClient } from "@clerk/nextjs/server";
import { AuthResult, AuthSession } from "./types";
import { logger } from "./utils";

/**
 * Get the current session from Clerk
 * 
 * @returns Promise with AuthResult containing the session or an error
 */
export const getCurrentSession = async (): Promise<AuthResult<AuthSession>> => {
  try {
    logger.info("Getting current session from Clerk");
    
    const authObject = await auth();
    const { userId, sessionId } = authObject;

    if (!userId || !sessionId) {
      logger.warn("No active session found");
      return {
        data: null,
        error: {
          code: "auth/no-session",
          message: "No active session"
        }
      };
    }

    const clerk = await clerkClient();
    const session = await clerk.sessions.getSession(sessionId);

    if (!session) {
      logger.warn(`Session not found with ID: ${sessionId}`);
      return {
        data: null,
        error: {
          code: "auth/session-not-found",
          message: "Session not found"
        }
      };
    }

    logger.info(`Successfully retrieved session: ${sessionId}`);
    
    return {
      data: {
        id: sessionId,
        userId,
        expiresAt: new Date(session.expireAt)
      },
      error: null
    };
  } catch (error) {
    logger.error("Error getting current session:", error);
    return {
      data: null,
      error: {
        code: "auth/unknown",
        message: "Failed to get current session",
        cause: error
      }
    };
  }
};

/**
 * Get a Supabase JWT token from Clerk
 * 
 * @returns Promise with AuthResult containing the token or an error
 */
export const getSupabaseToken = async (): Promise<AuthResult<string>> => {
  try {
    logger.info("Getting Supabase token from Clerk");
    
    const sessionResult = await getCurrentSession();
    
    if (sessionResult.error) {
      logger.warn("Failed to get session for Supabase token", sessionResult.error);
      return {
        data: null,
        error: sessionResult.error
      };
    }

    const { id: sessionId } = sessionResult.data;
    
    const clerk = await clerkClient();
    const tokenResponse = await clerk.sessions.getToken(sessionId, "supabase");

    if (!tokenResponse || !tokenResponse.jwt) {
      logger.warn("No JWT returned from Clerk for Supabase");
      return {
        data: null,
        error: {
          code: "auth/no-token",
          message: "No Supabase token returned from Clerk"
        }
      };
    }

    logger.info("Successfully retrieved Supabase token");
    
    return {
      data: tokenResponse.jwt,
      error: null
    };
  } catch (error) {
    logger.error("Error getting Supabase token:", error);
    return {
      data: null,
      error: {
        code: "auth/token-error",
        message: "Failed to get Supabase token",
        cause: error
      }
    };
  }
};