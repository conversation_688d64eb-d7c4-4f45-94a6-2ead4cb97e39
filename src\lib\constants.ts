// noinspection JSUnusedGlobalSymbols

/**
 * Application Constants
 * This file contains all text constants used throughout the application.
 */

// ===== ROUTES =====
export const ROUTES = {
    // Public Routes
    HOME: "/",
    LOGIN: "/login",
    SIGNUP: "/signup",
    ABOUT: "/about",
    PRIVACY: "/privacy",
    TERMS: "/terms",

    // Protected Routes
    GRAPHS: "/graphs",
    GRAPH: "/graph",
    GRAPH_CHAT: (graphId: string): string => `/graph/${graphId}/chat`,
    GRAPH_VIEW: (graphId: string): string => `/graph/${graphId}/graph`,
    GRAPH_SETTINGS: (graphId: string): string => `/graph/${graphId}/settings`,
    ACCOUNT: "/account",
};

// ===== API ENDPOINTS =====
export const API = {
    CLERK_WEBHOOK: "/api/auth/clerk-webhook",
    AUTH: "/api/auth",
    CHAT: "/api/chat",
    GRAPHS: "/api/graphs",
    UPDATE_NODE: "/api/node-update",
    GRAPH: (id: string): string => `/api/graphs/${id}`,
    NODES: (graphId: string): string => `/api/graphs/${graphId}/nodes`,
    NODE: (graphId: string, nodeId: string): string => `/api/graphs/${graphId}/nodes/${nodeId}`,
    EDGES: (graphId: string): string => `/api/graphs/${graphId}/edges`,
    EDGE: (graphId: string, edgeId: string): string => `/api/graphs/${graphId}/edges/${edgeId}`,
};

// ===== MIDDLEWARE LOGGING =====
export const MIDDLEWARE_LOGS = {
    CHECKING_ROUTE: "[Middleware] Checking route:",
    PUBLIC_ROUTE: "[Middleware] Public route:",
    PROTECTED_ROUTE: "[Middleware] Protected route:",
    AUTH_PATH_DETECTED: "[Middleware] Auth path detected:",
    USER_ID: "[Middleware] User ID:",
    NOT_AUTHENTICATED: "not authenticated",
    USER_AUTHENTICATED: "[Middleware] User authenticated",
    PROCESSING_REQUEST: "processing request",
    REDIRECTING_TO_LOGIN:
        "[Middleware] User not authenticated, redirecting to login",
    REDIRECTING_TO: "[Middleware] Redirecting to:",
    GETTING_SUPABASE_TOKEN: "[Middleware] Getting Supabase token for user",
    TOKEN_OBTAINED: "[Middleware] Supabase token obtained successfully",
    NO_TOKEN: "[Middleware] No Supabase token obtained",
    ERROR_TOKEN: "[Middleware] Error getting Supabase token:",
    CONTINUING: "[Middleware] Continuing without modifications",
    DETECTED_REDIRECT_LOOP: "[Middleware] Detected redirect loop in URL:",
    DETECTED_NESTED_REDIRECT: "[Middleware] Detected nested redirect in URL:",
    INVALID_REDIRECT_URL: "[Middleware] Invalid redirect URL:",
    CLEANING_REDIRECT_LOOP:
        "[Middleware] Cleaning up redirect loop, redirecting to:",
    ALREADY_ON_LOGIN: "[Middleware] Already on login page, allowing access",
    SESSION_ID_ADDED: "[Middleware] Added session ID to headers",
};

// ===== CLERK PROVIDER LOGGING =====
export const CLERK_LOGS = {
    COMPONENT_MOUNTED: "[ClerkProvider] Component mounted",
    USER_SIGNED_IN: "[ClerkProvider] User signed in event detected",
    USER_SIGNED_OUT: "[ClerkProvider] User signed out event detected",
    SESSION_CREATED: "[ClerkProvider] Session created event detected",
};

// ===== LOGIN PAGE =====
export const LOGIN_PAGE = {
    COMPONENT_MOUNTED: "[LoginPage] Component mounted",
    TITLE: "Sign In to GraphChat",
    SUBTITLE: "Welcome back! Sign in to continue.",
};

// ===== SIGNUP PAGE =====
export const SIGNUP_PAGE = {
    TITLE: "Sign Up for GraphChat",
    SUBTITLE: "Create your account to get started.",
};

// ===== GRAPHS PAGE =====
export const GRAPHS_PAGE = {
    TITLE: "Your Graphs",
    LOADING: "Loading your graphs...",
    CREATE_NEW: "Create New Graph",
    LOADING_AUTH: "Loading authentication state...",
    AUTH_REQUIRED_TITLE: "Authentication Required",
    AUTH_REQUIRED_MESSAGE: "You need to be signed in to view this page.",
    SIGN_IN: "Sign in",
};

// ===== METADATA =====
export const METADATA = {
    APP_TITLE: "GraphChat",
    APP_DESCRIPTION: "Chat with your graph data",
    LOGIN_TITLE: "Sign In | GraphChat",
    LOGIN_DESCRIPTION: "Sign in to your account",
    SIGNUP_TITLE: "Sign Up | GraphChat",
    SIGNUP_DESCRIPTION: "Create a new account",
};

// ===== CLERK WEBHOOK =====
export const WEBHOOK_LOGS = {
    INVALID_SIGNATURE: "Invalid webhook signature",
    USER_CREATED: "user.created event received",
    CREATING_USER: "Creating user in Supabase",
    USER_UPDATED: "user.updated event received",
    UPDATING_USER: "Updating user in Supabase",
};

// ===== UI ELEMENTS =====
export const UI = {
    // Button text
    BUTTONS: {
        SIGN_IN: "Sign In",
        SIGN_UP: "Sign Up",
        SIGN_OUT: "Sign Out",
        SAVE: "Save",
        CANCEL: "Cancel",
        CREATE: "Create",
        DELETE: "Delete",
        OPEN: "Open",
    },

    // Form labels
    FORM: {
        EMAIL: "Email",
        PASSWORD: "Password",
        NAME: "Name",
    },
};

// ===== ERROR MESSAGES =====
export const ERRORS = {
    AUTHENTICATION_FAILED: "Authentication failed. Please try again.",
    SESSION_EXPIRED: "Your session has expired. Please sign in again.",
    INVALID_CREDENTIALS: "Invalid email or password.",
    SERVER_ERROR: "Server error. Please try again later.",
    CHAT_GENERATION_FAILED: "Failed to generate chat response. Please try again.",
    CHAT_ERROR: "Sorry, there was an error processing your request.",
    ERROR_SENDING_MESSAGE: "Error sending message:",
};

// ===== CHAT MESSAGES =====
export const CHAT = {
    EMPTY_INPUT: "Input cannot be empty",
    SENDING_MESSAGE: "Sending message...",
    LOADING_INDICATOR: "AI is thinking...",
    CHAT_MESSAGE_FINISHED: "Chat message finished:",

    // Message roles
    ROLES: {
        SYSTEM: "system" as const,
        USER: "user" as const,
        ASSISTANT: "assistant" as const,
        DATA: "data" as const,
    },

    // Empty content placeholder
    EMPTY_CONTENT: "",
};

// Edge styling constants
export const EDGE = {
    // Colors
    COLORS: {
        DEFAULT: "#888888",
        HIGHLIGHTED: "rgb(0, 168, 11)",
        BLOCKED: "red",
        FILTERED: "#3b82f6",
        SELECTED: "#3b82f6",
    },
    // Widths
    WIDTHS: {
        // Default width for inactive edges (3px)
        DEFAULT: 3,
        // Width for edges that are part of an active path (3px)
        HIGHLIGHTED: 3,
        // Width for edges that are explicitly selected by the user (4px)
        SELECTED: 4,
    },
    // Animation
    ANIMATION: {
        // Whether to animate selected edges
        SELECTED: true,
        // Whether to animate path edges
        HIGHLIGHTED: true,
        // Whether to animate filtered edges
        FILTERED: true,
    },
} as const;

// For backward compatibility
export const EDGE_COLORS = EDGE.COLORS;
export const EDGE_WIDTHS = EDGE.WIDTHS;

// Node styling constants
export const NODE = {
    // Background colors
    BACKGROUNDS: {
        // Default background for nodes
        DEFAULT: "#FFFFFF",
        // Background for highlighted nodes (in path)
        HIGHLIGHTED: "#FFFFFF",
        // Background for selected nodes
        SELECTED: "#FFFFFF",
    },
    // Border widths
    WIDTHS: {
        // Default border width
        DEFAULT: 3,
        // Border width for highlighted nodes (in path)
        HIGHLIGHTED: 3,
        // Border width for selected nodes
        SELECTED: 4,
    },
    // Complete border styles
    BORDER: {
        // Default border style
        DEFAULT: '3px solid #222138',
        // Border style for highlighted nodes (in path)
        HIGHLIGHTED: '4px solid #006a20',
        // Border style for selected nodes
        SELECTED: '5px solid #3b82f6',
    },
    // Animation
    ANIMATION: {
        // Whether to animate selected nodes
        SELECTED: false,
        // Whether to animate highlighted nodes
        HIGHLIGHTED: false,
    },
} as const;

// For backward compatibility
export const NODE_BACKGROUNDS = NODE.BACKGROUNDS;
export const NODE_WIDTHS = NODE.WIDTHS;
export const NODE_BORDER = NODE.BORDER;

// Node tags constants
export const NODE_TAGS = {
    // Predefined tags
    DEFAULTS: [
        {
            id: 'thumbtack',
            icon: 'LiaThumbtackSolid',
            label: 'Pin',
            color: '#ef4444' // Red
        },
        {
            id: 'thumbs-up',
            icon: 'FaRegThumbsUp',
            label: 'Approve',
            color: '#10b981' // Green
        },
        {
            id: 'thumbs-down',
            icon: 'FaRegThumbsDown',
            label: 'Disapprove',
            color: '#f59e0b' // Amber
        },
        {
            id: 'lightbulb',
            icon: 'FaRegLightbulb',
            label: 'Idea',
            color: '#06b6d4' // Cyan
        },
        {
            id: 'error',
            icon: 'VscError',
            label: 'Error',
            color: '#ef4444' // Red
        },
        {
            id: 'info',
            icon: 'VscInfo',
            label: 'Information',
            color: '#3b82f6' // Blue
        },
        {
            id: 'show',
            icon: 'BiSolidShow',
            label: 'Show',
            color: '#8b5cf6' // Purple
        },
        {
            id: 'hide',
            icon: 'BiSolidHide',
            label: 'Hide',
            color: '#6b7280' // Gray
        },
        {
            id: 'fire',
            icon: 'BsFire',
            label: 'Hot',
            color: '#f97316' // Orange
        },
    ],
    // Default color for custom tags
    DEFAULT_COLOR: '#3b82f6', // Blue
} as const;

export enum OpenAIModel {
    GPT_4O = 'gpt-4o',
    GPT_4O_MINI = 'gpt-4o-mini',
    GPT_41 = 'gpt-4.1-2025-04-14',
    GPT_41_MINI = 'gpt-4.1-mini-2025-04-14',
    DEFAULT = 'gpt-4o-mini'
}

export enum AnthropicModel {
    CLAUDE_3_5_SONNET = 'claude-3-5-sonnet-latest',
    CLAUDE_3_5_HAIKU = 'claude-3-5-haiku-latest',
    CLAUDE_3_7_SONNET = 'claude-3-7-sonnet-latest',
    DEFAULT = 'claude-3-5-haiku-latest'
}

export enum GoogleModel {
    GEMINI_1_5_PRO = 'gemini-1.5-pro',
    GEMINI_2_0_FLASH = 'gemini-2.0-flash-001',
    GEMINI_2_0_FLASH_LITE = 'gemini-2.0-flash-lite-001',
    GEMINI_2_5_FLASH_PREVIEW = 'gemini-2.5-flash-preview-04-17',
    DEFAULT = 'gemini-1.5-pro'
}

export enum PerplexityModel {
    SONAR = 'sonar',
    SONAR_PRO = 'sonar-pro',
    DEFAULT = 'sonar'
}

export enum Providers {
    OPENAI = 'openai',
    ANTHROPIC = 'anthropic',
    GOOGLE = 'google',
    PERPLEXITY = 'perplexity',
}

export enum ProviderTitles {
    OPENAI = 'OpenAI',
    ANTHROPIC = 'Anthropic',
    GOOGLE = 'Google',
    PERPLEXITY = 'Perplexity',
}

export const PROVIDERS_MAP = {
    [Providers.OPENAI]: OpenAIModel,
    [Providers.ANTHROPIC]: AnthropicModel,
    [Providers.GOOGLE]: GoogleModel,
    [Providers.PERPLEXITY]: PerplexityModel,
};

export enum MaxTokens {
    T100 = 100,
    T1000 = 1000,
    T2000 = 2000,
    T4000 = 4000,
    T8000 = 8000,
    T16000 = 16000
}