import { NodeDTO as Node, CreateNodeDTO, UpdateNodeDTO } from "@/types/dto";

/**
 * Interface for Node repository
 * Defines methods for interacting with node data
 */
export interface INodeRepository {
  /**
   * Find a node by ID
   * @param id - The node ID
   * @returns The node or null if not found
   */
  findById(id: string): Promise<Node | null>;
  
  /**
   * Find all nodes for a specific graph
   * @param graphId - The graph ID
   * @returns Array of nodes
   */
  findByGraphId(graphId: string): Promise<Node[]>;
  
  /**
   * Create a new node
   * @param data - The node data
   * @returns The created node
   */
  create(data: CreateNodeDTO): Promise<Node>;
  
  /**
   * Update an existing node
   * @param id - The node ID
   * @param data - The updated node data
   * @returns The updated node
   */
  update(id: string, data: UpdateNodeDTO): Promise<Node>;
  
  /**
   * Delete a node
   * @param id - The node ID
   * @returns True if successful, false otherwise
   */
  delete(id: string): Promise<boolean>;
  
  /**
   * Find nodes with similar embeddings
   * @param graphId - The graph ID
   * @param embedding - The embedding vector to compare
   * @param limit - Maximum number of results
   * @returns Array of nodes with similarity scores
   */
  findSimilar(graphId: string, embedding: number[], limit?: number): Promise<Array<Node & { similarity: number }>>;
}
