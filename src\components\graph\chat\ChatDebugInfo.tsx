import React from "react";

interface ChatDebugInfoProps {
  /**
   * Whether the chat is currently loading
   */
  isLoading: boolean;

  /**
   * Number of messages in the chat
   */
  messageCount: number;
}

/**
 * Component that displays debug information about the chat state
 * Hidden in production by default
 */
export const ChatDebugInfo: React.FC<ChatDebugInfoProps> = ({
  isLoading,
  messageCount,
}) => {
  return (
    <div className="p-2 border-t border-gray-800 text-xs text-gray-600 hidden">
      <p>Loading: {isLoading ? "Yes" : "No"}</p>
      <p>Messages: {messageCount}</p>
    </div>
  );
};
