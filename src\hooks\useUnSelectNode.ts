// src/hooks/useUnselectNode.ts
import { useCallback } from "react";
import { useGraph } from "@/contexts/GraphContext";

/**
 * Custom hook to handle background pane clicks
 * Unselects the current node when clicking outside of any node
 */
export const useUnselectNode = () => {
  const { setSelectedNodeId } = useGraph();

  // Handle background pane click
  return useCallback((): void => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);
};
