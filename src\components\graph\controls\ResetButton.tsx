import { FiRefreshCw } from "react-icons/fi";
import { ControlButton } from "./ControlButton";
import React from "react";
import { useResetButtonLogic } from "./hooks/useResetButtonLogic";

export const ResetButton = () => {
  const { resetGraph, title } = useResetButtonLogic();
  return (
    <ControlButton
      icon={<FiRefreshCw className="h-4 w-4" />}
      onClick={resetGraph}
      title={title}
      className="bg-green-600 hover:bg-green-700 !bg-opacity-100"
    />
  );
};