"use client";

import { createContext, useContext, ReactNode } from "react";
import { useSupabase } from "@/hooks/useSupabase";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "@/types";

interface SupabaseContextType {
  client: SupabaseClient<Database> | null;
  isLoading: boolean;
  error: Error | null;
  refreshToken: () => void;
  isAuthenticated: boolean;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

interface SupabaseProviderProps {
  children: ReactNode;
}

/**
 * Провайдер для Supabase клиента
 * 
 * Предоставляет доступ к аутентифицированному Supabase клиенту через контекст
 */
export function SupabaseProvider({ children }: SupabaseProviderProps): JSX.Element {
  const supabaseContext = useSupabase();

  return (
    <SupabaseContext.Provider value={supabaseContext}>
      {children}
    </SupabaseContext.Provider>
  );
}

/**
 * Хук для использования Supabase клиента из контекста
 * 
 * @returns Объект с Supabase клиентом и дополнительными данными
 */
export function useSupabaseContext(): SupabaseContextType {
  const context = useContext(SupabaseContext);
  
  if (context === undefined) {
    throw new Error("useSupabaseContext must be used within a SupabaseProvider");
  }
  
  return context;
}
