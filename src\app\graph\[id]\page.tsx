"use client";

import React from "react";
import { SplitView } from "@/components/graph/SplitView";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/lib/constants";
import { GraphProvider } from "@/contexts/GraphContext";
import { useGraphQuery } from "@/hooks/useGraphQuery";

interface GraphPageProps {
  params: {
    id: string;
  };
}

export default function GraphPage({ params }: GraphPageProps): JSX.Element {
  const { id: graphId } = params;
  const router = useRouter();
  const { isLoading, graphExists, graphData, error } = useGraphQuery(graphId);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <p className="text-lg">Loading graph...</p>
      </div>
    );
  }

  // If there's an error, show it
  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="bg-red-800/20 p-6 rounded-lg max-w-md text-center">
          <h2 className="text-xl font-bold mb-4">Error</h2>
          <p className="mb-4">{error}</p>
          <button 
            onClick={() => router.push(ROUTES.GRAPHS)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            Return to graph list
          </button>
        </div>
      </div>
    );
  }

  // If graph exists, show the split view
  if (graphExists && graphData) {
    return (
      <GraphProvider initialGraph={graphData}>
        <SplitView graphId={graphId} />
      </GraphProvider>
    );
  }

  // Fallback while redirecting
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <p className="text-lg">Please wait...</p>
    </div>
  );
}
