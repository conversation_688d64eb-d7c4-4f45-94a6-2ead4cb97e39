import { getSupabaseClient } from "@/lib/supabase-client";
import { TagDTO, CreateTagDTO, UpdateTagDTO } from "@/types/dto";
import { ITagRepository } from "@/repositories";

/**
 * Supabase implementation of the Tag repository
 */
export class SupabaseTagRepository implements ITagRepository {
  private supabase;
  private sessionId?: string;

  constructor(sessionId?: string) {
    this.sessionId = sessionId;
    this.supabase = getSupabaseClient(sessionId);
  }

  /**
   * Find a tag by ID
   */
  async findById(id: string): Promise<TagDTO | null> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('tags')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return null;

    return this.mapToTagDTO(data);
  }

  /**
   * Find all tags for a specific user
   */
  async findByUserId(userId: string): Promise<TagDTO[]> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('tags')
      .select('*')
      .eq('user_id', userId)
      .order('label', { ascending: true });

    if (error || !data) return [];

    return data.map(this.mapToTagDTO);
  }

  /**
   * Find all system tags (not associated with a specific user)
   */
  async findSystemTags(): Promise<TagDTO[]> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('tags')
      .select('*')
      .is('user_id', null)
      .order('label', { ascending: true });

    if (error || !data) return [];

    return data.map(this.mapToTagDTO);
  }

  /**
   * Create a new tag
   */
  async create(data: CreateTagDTO): Promise<TagDTO> {
    const tagData = {
      label: data.label,
      icon: data.icon || null,
      color: data.color || '#3b82f6', // Default blue color
      short_label: data.shortLabel || null,
      user_id: data.userId || null,
    };

    const client = await this.supabase;
    const { data: newTag, error } = await client
      .from('tags')
      .insert(tagData)
      .select()
      .single();

    if (error || !newTag) {
      throw new Error(`Failed to create tag: ${error?.message}`);
    }

    return this.mapToTagDTO(newTag);
  }

  /**
   * Update an existing tag
   */
  async update(id: string, data: UpdateTagDTO): Promise<TagDTO> {
    const updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString(),
    };

    if (data.label !== undefined) updateData.label = data.label;
    if (data.icon !== undefined) updateData.icon = data.icon;
    if (data.color !== undefined) updateData.color = data.color;
    if (data.shortLabel !== undefined) updateData.short_label = data.shortLabel;

    const client = await this.supabase;
    const { data: updatedTag, error } = await client
      .from('tags')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !updatedTag) {
      throw new Error(`Failed to update tag: ${error?.message}`);
    }

    return this.mapToTagDTO(updatedTag);
  }

  /**
   * Delete a tag
   */
  async delete(id: string): Promise<boolean> {
    const client = await this.supabase;
    const { error } = await client
      .from('tags')
      .delete()
      .eq('id', id);

    return !error;
  }

  /**
   * Map Supabase data to TagDTO
   */
  private mapToTagDTO(data: Record<string, unknown>): TagDTO {
    return {
      id: data.id as string,
      label: data.label as string,
      icon: (data.icon as string) || undefined,
      color: (data.color as string) || '#3b82f6',
      shortLabel: (data.short_label as string) || undefined,
      createdAt: new Date(data.created_at as string),
      updatedAt: new Date(data.updated_at as string),
      userId: (data.user_id as string) || undefined,
    };
  }
}
