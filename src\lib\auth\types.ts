/**
 * Authentication Types
 */

export interface AuthUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
}

export interface AuthSession {
  id: string;
  userId: string;
  expiresAt: Date;
}

export interface SupabaseToken {
  token: string;
  expiresAt: number;
}

export interface AuthError {
  code: string;
  message: string;
  cause?: unknown;
}

export type AuthResult<T> = {
  data: T;
  error: null;
} | {
  data: null;
  error: AuthError;
};
