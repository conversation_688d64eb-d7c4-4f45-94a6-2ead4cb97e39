"use client";

import React, { useImperativeHandle } from "react";
import {
  FiZoomIn,
  Fi<PERSON>oomOut,
  FiMaximize,
  <PERSON>T<PERSON>gleRight,
  FiToggleLeft,
} from "react-icons/fi";
import { MdPanTool } from "react-icons/md";
import { ControlButton } from "./ControlButton";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphRef } from "@/contexts/GraphRefContext";
import { useReactFlow } from "@xyflow/react";

/**
 * Component for zoom and interactivity controls
 */
export const ZoomControls: React.FC = () => {
  const { isInteractive, setIsInteractive, isPanEnabled, togglePan } = useGraph();
  const { graphRef } = useGraphRef();
  const reactFlow = useReactFlow();

  // Common class for zoom control buttons
  const zoomButtonClass = "bg-gray-800 hover:bg-gray-700 !bg-opacity-100";

  // <PERSON><PERSON> functions for zoom controls
  const handleZoomIn = () => {
    if (graphRef.current) {
      graphRef.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (graphRef.current) {
      graphRef.current.zoomOut();
    }
  };

  const handleFitView = () => {
    if (graphRef.current) {
      graphRef.current.fitView();
    }
  };

  const toggleInteractivity = () => {
    setIsInteractive(!isInteractive);
    if (graphRef.current) {
      graphRef.current.toggleInteractivity(!isInteractive);
    }
  };

  const handleTogglePan = () => {
    togglePan();
  };

  // Expose methods via ref
  useImperativeHandle(graphRef, () => ({
    zoomIn: () => {
      if (reactFlow) {
        reactFlow.zoomIn().then(() => { /* do nothing */ });
      }
    },
    zoomOut: () => {
      if (reactFlow) {
        reactFlow.zoomOut().then(() => { /* do nothing */ });
      }
    },
    fitView: () => {
      if (reactFlow) {
        reactFlow.fitView({ padding: 0.2 }).then(() => { /* do nothing */ });
      }
    },
    toggleInteractivity: (value: boolean) => {
      // This method will be called from SplitView component
      if (setIsInteractive) {
        setIsInteractive(value);
      }
    },
  }));

  return (
    <div className="flex items-center space-x-2 mr-2">
      <ControlButton
        onClick={handleZoomIn}
        title="Zoom In"
        icon={<FiZoomIn className="h-4 w-4" />}
        className={zoomButtonClass}
      />

      <ControlButton
        onClick={handleZoomOut}
        title="Zoom Out"
        icon={<FiZoomOut className="h-4 w-4" />}
        className={zoomButtonClass}
      />

      <ControlButton
        onClick={handleFitView}
        title="Fit View"
        icon={<FiMaximize className="h-4 w-4" />}
        className={zoomButtonClass}
      />

      <ControlButton
        onClick={toggleInteractivity}
        title={isInteractive ? "Disable Interactivity" : "Enable Interactivity"}
        icon={
          isInteractive ? (
            <FiToggleRight className="h-4 w-4" />
          ) : (
            <FiToggleLeft className="h-4 w-4" />
          )
        }
        className={zoomButtonClass}
      />

      <ControlButton
        onClick={handleTogglePan}
        title={isPanEnabled ? "Disable Pan" : "Enable Pan"}
        icon={
          isPanEnabled ? (
            <MdPanTool className="h-4 w-4" />
          ) : (
            <MdPanTool className="h-4 w-4 text-gray-400" />
          )
        }
        className={zoomButtonClass}
      />
    </div>
  );
};
