"use client";

import React from "react";
import { useCurrentNode } from "@/hooks/useCurrentNode";
import { useGraph } from "@/contexts/GraphContext";

/**
 * Debug component to display information about the current node
 */
export const CurrentNodeDebug: React.FC = () => {
  const { currentNode, currentNodeId } = useCurrentNode();
  const { nodes } = useGraph();

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg z-50 max-w-xs">
      <h3 className="text-lg font-semibold mb-2">Current Node Debug</h3>
      <div className="text-sm">
        <p className="mb-1">
          <span className="font-medium">Total Nodes:</span> {nodes.length}
        </p>
        <p className="mb-1">
          <span className="font-medium">Current Node ID:</span>{" "}
          {currentNodeId || "None"}
        </p>
        {currentNode ? (
          <>
            <p className="mb-1">
              <span className="font-medium">Label:</span>
              {` ${currentNode.data?.label || "No Label"}`}
            </p>
            <p className="mb-1">
              <span className="font-medium">Position:</span> x:{" "}
              {Math.round(currentNode.position.x)}, y:{" "}
              {Math.round(currentNode.position.y)}
            </p>
            <p className="mb-1">
              <span className="font-medium">Type:</span>{" "}
              {currentNode.type || "default"}
            </p>
          </>
        ) : (
          <p className="italic text-gray-400">No current node selected</p>
        )}
      </div>
    </div>
  );
};

export default CurrentNodeDebug;
