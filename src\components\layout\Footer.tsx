import React from "react";

interface FooterProps {
  content?: string;
  graphId?: string;
}

export const Footer: React.FC<FooterProps> = ({ content }) => {

  return (
    <footer className="h-[60px] bg-gray-900 flex items-center justify-between px-4 border-t border-gray-800">
      <div className="text-white">{content}</div>

      <div className="flex items-center space-x-4">
      </div>
    </footer>
  );
};
