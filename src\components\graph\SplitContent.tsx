"use client";

import React, { useRef, useState, useCallback, useEffect } from "react";
import { GraphPanel } from "./GraphPanel";
import { ResizableDivider } from "../layout/ResizableDivider";
import { ChatPanel } from "./ChatPanel";
import { useGraph } from "@/contexts/GraphContext";
import { ClientStyleWrapper } from "@/components/providers/ClientStyleWrapper";

interface SplitContentProps {
  graphId: string;
}

export const SplitContent: React.FC<SplitContentProps> = ({ graphId }) => {
  // Refs for resizing
  const containerRef = useRef<HTMLDivElement>(null);
  const dividerRef = useRef<HTMLDivElement>(null);

  // State for panel sizes
  const [leftPanelWidth, setLeftPanelWidth] = useState<number>(50); // 50% initially
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [transitionEnabled, setTransitionEnabled] = useState<boolean>(true);
  const { fitView } = useGraph();

  // Start dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    // Disable transition during dragging for more responsive movement
    setTransitionEnabled(false);
    setIsDragging(true);
  }, []);

  // Handle dragging
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return;

      // Request animation frame for smoother updates
      requestAnimationFrame(() => {
        if (!containerRef.current) return;

        const containerRect = containerRef.current.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const mouseX = e.clientX - containerRect.left;

        // Calculate percentage (constrain between 20% and 80%)
        let newWidthPercent = (mouseX / containerWidth) * 100;
        newWidthPercent = Math.max(20, Math.min(80, newWidthPercent));

        setLeftPanelWidth(newWidthPercent);
      });
    },
    [isDragging],
  );

  // End dragging
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    // Re-enable transition after dragging ends
    setTimeout(() => {
      setTransitionEnabled(true);
    }, 50);
    setTimeout(() => {
      fitView();
    }, 100);
  }, [fitView]);

  // Add and remove event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    } else {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Обновляем размеры при изменении размера окна
  useEffect(() => {
    const handleResize = () => {
      if (fitView) {
        setTimeout(() => {
          fitView();
        }, 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [fitView]);

  return (
    <ClientStyleWrapper>
      <div 
        ref={containerRef} 
        className="flex flex-1 w-full overflow-hidden"
        style={{ 
          height: "calc(100vh - 160px)",
          minHeight: "500px"
        }}
      >
        {/* Graph Panel - dynamic width */}
        <GraphPanel
          width={leftPanelWidth}
          transitionEnabled={transitionEnabled}
        />

        {/* Resizable Divider */}
        <ResizableDivider
          dividerRef={dividerRef}
          handleMouseDown={handleMouseDown}
          transitionEnabled={transitionEnabled}
        />

        {/* Chat Panel - dynamic width */}
        <ChatPanel
          graphId={graphId}
          width={100 - leftPanelWidth}
          transitionEnabled={transitionEnabled}
        />
      </div>
    </ClientStyleWrapper>
  );
};
