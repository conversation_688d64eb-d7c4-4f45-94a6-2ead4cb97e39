"use client";

import { IoMdUndo } from "react-icons/io";
import { ControlButton } from "./ControlButton";
import React from "react";
import { useUndoButtonLogic } from "./hooks/useUndoButtonLogic";

export const UndoButton = () => {
  const { undo, canUndo, title } = useUndoButtonLogic();
  return (
    <ControlButton
      icon={<IoMdUndo className="h-4 w-4" />}
      onClick={undo}
      disabled={!canUndo}
      title={title}
    />
  );
};