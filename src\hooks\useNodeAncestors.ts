import { useCallback } from "react";
import useNodeRelations from "./useNodeRelations";
import { Node, Edge } from "@xyflow/react";
import { NodeTag } from "@/utils/nodeBuilder";

export interface NodeAncestor {
  id: string;
  depth: number;
  node: Node;
  path: string[]; // Track the path to this ancestor
}

function filterAncestors(
  finalAncestors: NodeAncestor[],
  edges: Edge[],
  nodes: Node[]
): NodeAncestor[] {
  // Create caches for faster search
  const edgeCache = new Map<string, Edge>();
  const nodeCache = new Map<string, Node>();
  
  // Pre-fill caches
  edges.forEach(edge => {
    const key = `${edge.source}-${edge.target}`;
    edgeCache.set(key, edge);
  });
  
  nodes.forEach(node => {
    nodeCache.set(node.id, node);
  });

  return finalAncestors.filter(ancestor => {
    if (ancestor.path.length < 2) {
      return true;
    }
    
    const path: string[] = [ancestor.path[0]];
    const filters: Set<string>[] = [];
    
    for (let index = 1; index < ancestor.path.length; index += 1) {
      const source = ancestor.path[index];
      const target = ancestor.path[index - 1];
      const edgeKey = `${source}-${target}`;
      const edge = edgeCache.get(edgeKey);
      
      // Check tagFilter
      const tagFilter: string[] = (edge?.data?.tagFilter as string[]) ?? [];
      if (tagFilter.length > 0) {
        // Use Set for fast search
        filters.push(new Set(tagFilter));
      }
      
      const node = nodeCache.get(source);
      
      // Check if matches filters
      if (filters.length === 0 || filters.every(filter => {
        const nodeTags = Array.isArray(node?.data?.tags) 
          ? node.data.tags.map((tag: NodeTag) => tag.id) 
          : [];
        return nodeTags.some(tagId => filter.has(tagId));
      })) {
        path.push(source);
      }
    }
    
    return path.length > 1 && path.includes(ancestor.node.id);
  });
}
/**
 * Hook for getting node ancestor chain
 * Returns array of ancestors sorted from root to current node
 */
export const useNodeAncestors = () => {
  const { getParentNodes } = useNodeRelations();

  /**
   * Gets the chain of ancestors for the specified node
   * @param nodeId ID of the node to find ancestors for
   * @returns Array of ancestors sorted from root to current node
   */
  return useCallback(
    (nodeId: string, nodes: Node[], edges: Edge[]): NodeAncestor[] => {
      const MAX_DEPTH = 100; // Limit on traversal depth
      const ancestorPaths = new Map<string, NodeAncestor[]>(); // Store all found paths for each node

      /**
       * Recursive function for traversing "up" through the graph.
       *
       * @param currentId - current node in traversal
       * @param currentPath - array of node ids forming path from nodeId to currentId
       * @param depth - current depth (number of transitions) from the source node
       * @param visited - set of already visited nodes to prevent cycles
       */
      function findPaths(
        currentId: string,
        currentPath: string[] = [],
        depth: number = 0,
        visited: Set<string> = new Set()
      ): void {
        // Prevent cycles
        if (visited.has(currentId)) return;
        
        // Optimization: modify existing Set instead of creating a new one
        visited.add(currentId);
        
        // Find node object
        const currentNode = nodes.find((node) => node.id === currentId);
        if (!currentNode) {
          visited.delete(currentId); // Important to remove from visited on exit
          return;
        }

        // Form path by adding current node (avoid [...spread] for better performance)
        currentPath.push(currentId);
        
        // Save current node as candidate
        let candidates = ancestorPaths.get(currentId);
        if (!candidates) {
          candidates = [];
          ancestorPaths.set(currentId, candidates);
        }
        
        candidates.push({
          id: currentId,
          depth,
          node: currentNode,
          path: [...currentPath], // Need a copy here as currentPath will change
        });

        // Check if we can go further
        if (depth < MAX_DEPTH) {
          // Find all parents of this node
          const parentIds = getParentNodes(currentId, edges);
          
          // Go through parents
          for (const parentId of parentIds) {
            // Find edge (parent -> current)
            const edgeToParent = edges.find(
              (e) => e.source === parentId && e.target === currentId
            );
            
            if (!edgeToParent || edgeToParent.data?.isBlocked === true) {
              continue;
            }
            
            // Move "up" to parent with recursive call
            findPaths(parentId, currentPath, depth + 1, visited);
          }
        }
        
        // Clean up for backtracking - remove current node from path and visited
        currentPath.pop();
        visited.delete(currentId);
      }

      // Start traversal from source node
      findPaths(nodeId);
      
      // Form final list of ancestors
      const finalAncestors: NodeAncestor[] = [];

      // Go through all collected candidates, select path with minimum depth for each node
      for (const [id, candidates] of Array.from(ancestorPaths.entries())) {
        // Exclude the source node itself from results
        if (id === nodeId) continue;
        const bestCandidate = candidates.reduce((prev, curr) =>
          curr.depth < prev.depth ? curr : prev
        );
        finalAncestors.push(bestCandidate);
      }
      
      // Sort by depth (ascending)
      const final: NodeAncestor[] = filterAncestors(
        finalAncestors,
        edges,
        nodes
      );
      return final.sort((a, b) => a.depth - b.depth);
    },
    [getParentNodes]
  );
};

export default useNodeAncestors;
