/* eslint-disable no-console */

import { getSupabaseClient } from "@/lib/supabase-client";
import { INodeRepository } from "@/repositories";
import { NodeDTO as Node, CreateNodeDTO, UpdateNodeDTO } from "@/types/dto";
import { NodeType } from "@/utils/nodeBuilder";

/**
 * Supabase implementation of the Node repository
 */
export class SupabaseNodeRepository implements INodeRepository {
  private supabase;

  constructor(supabaseToken?: string) {
    if (supabaseToken) {
      this.supabase = getSupabaseClient().then(client => client);
    } else {
      this.supabase = getSupabaseClient();
    }
  }

  /**
   * Find a node by ID
   */
  async findById(id: string): Promise<Node | null> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_nodes')
      .select('*, chat_node_metadata(*)')
      .eq('id', id)
      .single();

    if (error || !data) return null;

    return this.mapToNode(data);
  }

  /**
   * Find all nodes for a graph
   */
  async findByGraphId(graphId: string): Promise<Node[]> {
    const client = await this.supabase;
    const { data, error } = await client
      .from('chat_nodes')
      .select('*, chat_node_metadata(*)')
      .eq('graph_id', graphId)
      .order('created_at', { ascending: true });

    if (error || !data) return [];

    return data.map(this.mapToNode);
  }

  /**
   * Create a new node
   */
  async create(data: CreateNodeDTO): Promise<Node> {
    // Convert to Supabase schema
    const nodeData = {
      graph_id: data.graphId,
      user_dialog: data.userDialog || '',
      assistant_dialog: data.assistantDialog || '',
      label: data.label,
      x: data.x,
      y: data.y,
      type: data.type || 'default',
      is_included: data.isIncluded !== false, // Default to true if not specified
      data: { 
        ...data.data || {},  
        nodeType: data?.data?.nodeType || NodeType.REGULAR // Set default node type
      }
    };

    // Create the node
    const client = await this.supabase;
    const { data: newNode, error } = await client
      .from('chat_nodes')
      .insert(nodeData)
      .select('*, chat_node_metadata(*)')
      .single();

    if (error || !newNode) {
      throw new Error(`Failed to create node: ${error?.message}`);
    }

    // If tags are provided, create node tags
    if (data.tags && data.tags.length > 0) {
      // Create metadata with tags if it doesn't exist
      const metadataData = {
        node_id: newNode.id,
        tags: data.tags,
        additional_data: {}
      };

      await client
        .from('chat_node_metadata')
        .upsert(metadataData)
        .select();
    }

    // Fetch the complete node with metadata
    const { data: completeNode } = await client
      .from('chat_nodes')
      .select('*, chat_node_metadata(*)')
      .eq('id', newNode.id)
      .single();

    return this.mapToNode(completeNode || newNode);
  }

  /**
   * Update an existing node
   */
  async update(id: string, data: UpdateNodeDTO): Promise<Node> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (data.label !== undefined) updateData.label = data.label;
    if (data.x !== undefined) updateData.x = data.x;
    if (data.y !== undefined) updateData.y = data.y;
    if (data.type !== undefined) updateData.type = data.type;
    if (data.userDialog !== undefined) updateData.user_dialog = data.userDialog;
    if (data.assistantDialog !== undefined) updateData.assistant_dialog = data.assistantDialog;
    if (data.isIncluded !== undefined) updateData.is_included = data.isIncluded;
    if (data.data !== undefined) updateData.data = data.data;

    // Update the node
    const client = await this.supabase;
    const { data: updatedNode, error } = await client
      .from('chat_nodes')
      .update(updateData)
      .eq('id', id)
      .select('*, chat_node_metadata(*)')
      .single();

    if (error || !updatedNode) {
      throw new Error(`Failed to update node: ${error?.message}`);
    }

    // If tags are provided, update node tags
    if (data.tags !== undefined) {
      // Get existing metadata
      const { data: existingMetadata } = await client
        .from('chat_node_metadata')
        .select('*')
        .eq('node_id', id)
        .single();

      if (existingMetadata) {
        // Update existing metadata
        await client
          .from('chat_node_metadata')
          .update({
            tags: data.tags,
          })
          .eq('node_id', id);
      } else if (data.tags.length > 0) {
        // Create new metadata if it doesn't exist and tags are provided
        await client
          .from('chat_node_metadata')
          .insert({
            node_id: id,
            tags: data.tags,
            additional_data: {}
          });
      }
    }

    // Fetch the complete updated node with metadata
    const { data: completeNode } = await client
      .from('chat_nodes')
      .select('*, chat_node_metadata(*)')
      .eq('id', id)
      .single();

    return this.mapToNode(completeNode || updatedNode);
  }

  /**
   * Delete a node
   */
  async delete(id: string): Promise<boolean> {
    const client = await this.supabase;
    const { error } = await client
      .from('chat_nodes')
      .delete()
      .eq('id', id);

    return !error;
  }

  /**
   * Find nodes with similar embeddings
   */
  async findSimilar(graphId: string, embedding: number[], limit: number = 5): Promise<Array<Node & { similarity: number }>> {
    // Use pgvector's <-> operator for cosine distance
    const client = await this.supabase;
    const { data, error } = await client.rpc('match_chat_nodes', {
      query_embedding: embedding,
      match_threshold: 0.7,
      match_count: limit,
      p_graph_id: graphId
    });

    if (error || !data) {
      console.error('Error finding similar nodes:', error);
      return [];
    }

    return data.map((item: any) => ({
      ...this.mapToNode(item),
      similarity: item.similarity
    }));
  }

  /**
   * Map Supabase data to Node
   */
  private mapToNode(data: Record<string, unknown>): Node {
    return {
      id: data.id as string,
      label: data.label as string || `Node ${(data.id as string).substring(0, 4)}`,
      x: data.x as number || 0,
      y: data.y as number || 0,
      type: data.type as string || 'default',
      userDialog: data.user_dialog as string || '',
      assistantDialog: data.assistant_dialog as string || '',
      tags: (data.chat_node_metadata as Record<string, unknown>)?.tags as string[] || [],
      isIncluded: data.is_included as boolean, // Default to true if not specified
      data: (data.chat_node_metadata as Record<string, unknown>)?.additional_data as Record<string, unknown> || {},
      createdAt: new Date(data.created_at as string),
      updatedAt: new Date(data.updated_at as string),
      graphId: data.graph_id as string
    };
  }
}
