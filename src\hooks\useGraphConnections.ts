import { useCallback, useRef, useMemo } from "react";
import {
  Connection,
  OnConnectStart,
  OnConnectEnd,
  HandleType,
  addEdge,
} from "@xyflow/react";
import { useGraph } from "@/contexts/GraphContext";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";
import { EdgeBuilder } from "@/utils/edgeBuilder";
import { NodeBuilder } from "@/utils/nodeBuilder";
import { nanoid } from "nanoid";

/**
 * Custom hook to manage graph connections
 * Handles connection start, connection end, and node connections
 * @returns Object containing onConnectStart, onConnectEnd, and onConnect functions
 */
export const useGraphConnections = () => {
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    reactFlowInstanceRef,
    bgColor,
    selectedNodeId,
    setSelectedNodeId,
    setCurrentNodeId,
    currentNodeId,
    messages,
  } = useGraph();

  const { saveToHistory } = useGraphHistory();

  // State for connection handling
  const connectingNodeId = useRef<{
    nodeId: string | null;
    handleId: string | null;
    handleType: HandleType | null;
  }>({
    nodeId: null,
    handleId: null,
    handleType: null,
  });

  // Handle connection start
  const onConnectStart = useCallback<OnConnectStart>((event, params) => {
    // Store connection parameters in the ref
    if (params && typeof params === "object") {
      connectingNodeId.current = {
        nodeId: params.nodeId,
        handleId: params.handleId,
        handleType: params.handleType,
      };
    }
  }, []);

  // Handle connection end
  const onConnectEnd = useCallback<OnConnectEnd>(
    (event) => {
      const { nodeId, handleId, handleType } = connectingNodeId.current;
      
      if (!nodeId || !handleType || !reactFlowInstanceRef.current) {
        // Reset the connecting node ref
        connectingNodeId.current = {
          nodeId: null,
          handleId: null,
          handleType: null,
        };
        return;
      }

      // Get the target element from the DOM
      const targetElement = event.target as Element;
      const targetIsPane = targetElement.classList.contains("react-flow__pane");

      if (targetIsPane) {
        // If target is the pane, create a new node
        const reactFlowBounds =
          reactFlowInstanceRef.current.screenToFlowPosition({
            x: (event as MouseEvent).clientX,
            y: (event as MouseEvent).clientY,
          });

        // Define node width and adjust position to center the node horizontally
        const nodeWidth = 180;
        const nodePosition = {
          x: reactFlowBounds.x - nodeWidth / 2, // Center horizontally
          y: reactFlowBounds.y, // Keep top aligned with mouse position
        };

        const newNodeId = nanoid();

        // Create a new node using NodeBuilder
        const newNode = NodeBuilder.createNodeAtPosition(
          newNodeId,
          nodePosition,
          "Empty Node"
        );

        // Save current state to history before adding the node
        saveToHistory({
          nodes: [...nodes],
          edges: [...edges],
          selectedNodeId,
          bgColor,
          currentNodeId,
          messages,
        });

        // Add the new node
        setNodes((nds) => [...nds, newNode]);

        // Set the new node as current and clear selection
        setCurrentNodeId(newNodeId);
        setSelectedNodeId(null);

        // Create a new edge based on the handle type
        const newEdge = EdgeBuilder.createCustomEdge(
          {
            id: nanoid(),
            source: handleType === "source" ? nodeId : newNodeId,
            target: handleType === "source" ? newNodeId : nodeId,
            sourceHandle:
              handleType === "source" ? handleId || undefined : undefined,
            targetHandle:
              handleType === "target" ? handleId || undefined : undefined,
          },
          {
            // New edges are never blocked by default
            isBlocked: false
          }
        );

        // Add the new edge
        setEdges((eds) => [...eds, newEdge]);
      }

      // Reset the connecting node ref
      connectingNodeId.current = {
        nodeId: null,
        handleId: null,
        handleType: null,
      };
    },
    [
      nodes,
      edges,
      setNodes,
      setEdges,
      reactFlowInstanceRef,
      saveToHistory,
      bgColor,
      selectedNodeId,
      setSelectedNodeId,
      setCurrentNodeId,
      currentNodeId,
      messages,
    ]
  );

  // Handle node connection
  const onConnect = useCallback(
    (connection: Connection) => {
      // Save current state to history before adding the edge
      saveToHistory({
        nodes: [...nodes],
        edges: [...edges],
        selectedNodeId,
        bgColor,
        currentNodeId,
        messages,
      });

      // Add the new edge
      setEdges((eds) => {
        // Check if there's already an edge between these nodes that might be blocked
        const existingEdge = eds.find(
          (edge) =>
            edge.source === connection.source &&
            edge.target === connection.target
        );

        // Create a new edge with styling, preserving blocked state if it exists
        const newEdge = EdgeBuilder.createCustomEdge(
          {
            id: nanoid(),
            source: connection.source,
            target: connection.target,
            sourceHandle: connection.sourceHandle || undefined,
            targetHandle: connection.targetHandle || undefined,
          },
          {
            // If there's an existing edge between these nodes, preserve its blocked state
            isBlocked: existingEdge?.data?.isBlocked === true
          }
        );

        return addEdge(newEdge, eds);
      });
    },
    [nodes, edges, setEdges, saveToHistory, bgColor, selectedNodeId, currentNodeId, messages]
  );

  // Return the connection handlers
  return useMemo(
    () => ({
      onConnectStart,
      onConnectEnd,
      onConnect,
    }),
    [onConnectStart, onConnectEnd, onConnect]
  );
};
