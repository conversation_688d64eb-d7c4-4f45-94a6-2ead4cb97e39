import React, { useMemo } from "react";
import { useResetGraph } from "@/hooks/useResetGraph";

export const useResetButtonLogic = () => {
  const resetGraph = useResetGraph();
  
  // Add keydown event listener for reset shortcut (Ctrl+R)
  const resetFn = React.useCallback((event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      event.preventDefault(); // Prevent browser's default behavior
      resetGraph();
    }
  }, [resetGraph]);
  
  // Set up event listener
  React.useEffect(() => {
    document.addEventListener("keydown", resetFn);
    return () => {
      document.removeEventListener("keydown", resetFn);
    };
  }, [resetFn]);

  return useMemo(() => ({
    resetGraph,
    title: "Reset Graph"
  }), [resetGraph]);
};