import React from "react";

interface ResizableDividerProps {
  dividerRef: React.RefObject<HTMLDivElement>;
  handleMouseDown: (e: React.MouseEvent) => void;
  transitionEnabled: boolean;
}

/**
 * Component for the resizable divider between panels
 */
export const ResizableDivider: React.FC<ResizableDividerProps> = ({
  dividerRef,
  handleMouseDown,
  transitionEnabled,
}) => {
  return (
    <div
      ref={dividerRef}
      className={`w-1 bg-gray-800 hover:bg-blue-600 cursor-col-resize flex items-center justify-center relative z-10 ${transitionEnabled ? "transition-colors duration-200" : ""}`}
      onMouseDown={handleMouseDown}
    >
      {/* Resize handle/lever */}
      <div className="absolute w-6 h-12 flex items-center justify-center">
        <div
          className={`w-4 h-8 bg-gray-700 rounded-full flex items-center justify-center hover:bg-blue-600 ${transitionEnabled ? "transition-colors duration-200" : ""}`}
        >
          <div className="w-0.5 h-4 bg-gray-400 mx-0.5"></div>
          <div className="w-0.5 h-4 bg-gray-400 mx-0.5"></div>
        </div>
      </div>
    </div>
  );
};
