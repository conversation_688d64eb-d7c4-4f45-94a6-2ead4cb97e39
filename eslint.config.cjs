// eslint.config.cjs
module.exports = [
  {
    // instead of --ext/.extensions:
    files: ["src/**/*.{js,jsx,ts,tsx}"],
    // instead of --ignore-path:
    ignores: ["node_modules/**"],

    languageOptions: {
      parser: require("@typescript-eslint/parser"),
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module",
        project: "./tsconfig.json",
        ecmaFeatures: { jsx: true },
      },
    },

    settings: {
      react: { version: "detect" },
    },

    plugins: {
      "@typescript-eslint": require("@typescript-eslint/eslint-plugin"),
      react:            require("eslint-plugin-react"),
      "react-hooks":    require("eslint-plugin-react-hooks"),
      import:           require("eslint-plugin-import"),
    },

    // instead of a top‑level reportUnusedDisableDirectives flag:
    linterOptions: {
      reportUnusedDisableDirectives: "error",
    },

    rules: {
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": [
        "error",
        { vars: "all", args: "after-used", ignoreRestSiblings: true },
      ],
      "react/jsx-uses-react":     "off",
      "react/react-in-jsx-scope": "off",
      "react-hooks/rules-of-hooks":  "error",
      "react-hooks/exhaustive-deps": "warn",
      "no-console": "warn",
    },
  },
];
