import { SignIn } from "@clerk/nextjs";
import Link from "next/link";
import { LOGIN_PAGE, ROUTES } from "@/lib/constants";

export default function LoginPage(): JSX.Element {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">{LOGIN_PAGE.TITLE}</h1>
          <p className="mt-2 text-gray-600">{LOGIN_PAGE.SUBTITLE}</p>
        </div>

        <SignIn
          forceRedirectUrl={ROUTES.GRAPHS}
          appearance={{
            elements: {
              formButtonPrimary:
                "bg-blue-500 hover:bg-blue-600 text-sm normal-case",
              card: "shadow-md",  
              footerAction: "text-blue-500 hover:text-blue-600",
            },
          }}
        />
        
        <div className="mt-6 text-center">
          <p className="text-gray-600">
           {`Don't have an account? `}
            <Link href={ROUTES.SIGNUP} className="text-blue-500 hover:text-blue-600 font-medium">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
