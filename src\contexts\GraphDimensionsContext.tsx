"use client";

import React, {
  createContext,
  useContext,
  useState,
  useC<PERSON>back,
  useMemo,
  ReactNode,
} from "react";

// Context type definition
interface GraphDimensionsContextType {
  graphWidth: number;
  graphHeight: number;
  containerWidth: number;
  containerHeight: number;
  setGraphDimensions: (width: number, height: number) => void;
}

// Create the context
const GraphDimensionsContext = createContext<
  GraphDimensionsContextType | undefined
>(undefined);

// Custom hook to use the graph dimensions context
export const useGraphDimensions = (): GraphDimensionsContextType => {
  const context = useContext(GraphDimensionsContext);
  if (!context) {
    throw new Error(
      "useGraphDimensions must be used within a GraphDimensionsProvider",
    );
  }
  return context;
};

// Context provider component
export const GraphDimensionsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // State for graph container dimensions
  const [graphWidth, setGraphWidth] = useState<number>(0);
  const [graphHeight, setGraphHeight] = useState<number>(0);

  // Default container dimensions (previously in GraphStore)
  const containerWidth = graphWidth > 0 ? graphWidth : 800; // Default width
  const containerHeight = graphHeight > 0 ? graphHeight : 600; // Default height

  // Function to set graph dimensions
  const setGraphDimensions = useCallback(
    (width: number, height: number): void => {
      setGraphWidth(width);
      setGraphHeight(height);
    },
    [],
  );

  // Create the context value object
  const contextValue = useMemo<GraphDimensionsContextType>(
    () => ({
      graphWidth,
      graphHeight,
      containerWidth,
      containerHeight,
      setGraphDimensions,
    }),
    [graphWidth, graphHeight, containerWidth, containerHeight, setGraphDimensions],
  );

  return (
    <GraphDimensionsContext.Provider value={contextValue}>
      {children}
    </GraphDimensionsContext.Provider>
  );
};
