import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import React from "react";
import { ROUTES } from "@/lib/constants";

export default async function Home(): Promise<React.ReactNode> {
  const { userId } = await auth();

  // If user is authenticated, redirect to graphs page
  if (userId) {
    redirect("/graphs");
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 bg-black text-white">
      <div className="max-w-4xl w-full text-center">
        <h1 className="text-4xl font-bold mb-6">Welcome to GraphChat</h1>
        <p className="text-xl mb-8">
          Create, visualize, and chat with your knowledge graphs in one place.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={ROUTES.LOGIN}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors"
          >
            Sign In
          </Link>
          <Link
            href="/signup"
            className="px-6 py-3 bg-gray-700 hover:bg-gray-800 rounded-lg font-medium transition-colors"
          >
            Create Account
          </Link>
        </div>

        <div className="mt-12">
          <Link href="/about" className="text-blue-400 hover:underline">
            Learn more about GraphChat
          </Link>
        </div>
      </div>
    </main>
  );
}
