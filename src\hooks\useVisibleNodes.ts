import {useCallback} from "react";
import {useReactFlow} from "@xyflow/react";
import {useGraph} from "@/contexts/GraphContext";
import {useGraphDimensions} from "@/contexts/GraphDimensionsContext";

/**
 * Hook to ensure all nodes remain visible within the viewport
 * Uses fitView when nodes go out of bounds
 */
export const useVisibleNodes = () => {
  const { nodes, fitView } = useGraph();
  const { graphWidth, graphHeight } = useGraphDimensions();
  const view = useReactFlow();

  return useCallback(() => {
    if (!nodes.length) return;

    // Get the current viewport
    const {x, y, zoom} = view.getViewport();

    // Calculate visible area in flow coordinates
    const visibleArea = {
      x: -x / zoom,
      y: -y / zoom,
      width: graphWidth / zoom,
      height: graphHeight / zoom,
    };

    // Check if any node is outside the visible area
    let anyNodeOutOfBounds = false;

    for (const node of nodes) {
      // Skip if node doesn't have position
      if (!node.position) continue;

      // Approximate node dimensions (use actual if available)
      const nodeWidth = (node.style?.width as number) || 180;
      const nodeHeight = (node.style?.height as number) || 40;

      // Check if node is outside visible area
      const isOutOfBounds =
          node.position.x < visibleArea.x ||
          node.position.y < visibleArea.y ||
          node.position.x + nodeWidth > visibleArea.x + visibleArea.width ||
          node.position.y + nodeHeight > visibleArea.y + visibleArea.height;

      if (isOutOfBounds) {
        anyNodeOutOfBounds = true;
        break;
      }
    }

    // If any node is out of bounds, use fitView to show all nodes
    if (anyNodeOutOfBounds) {
      fitView();
    }
  }, [nodes, graphWidth, graphHeight, view, fitView]);
};

export default useVisibleNodes;
