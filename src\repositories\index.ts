// noinspection JSUnusedGlobalSymbols

import {
  IUserRepository,
  IGraphRepository,
  INodeRepository,
  IEdgeRepository,
  INodeMetadataRepository,
  ITagRepository
} from "./interfaces";

import { SupabaseUserRepository } from "./implementations/SupabaseUserRepository";
import { SupabaseGraphRepository } from "./implementations/SupabaseGraphRepository";
import { SupabaseNodeRepository } from "./implementations/SupabaseNodeRepository";
import { SupabaseEdgeRepository } from "./implementations/SupabaseEdgeRepository";
import { SupabaseNodeMetadataRepository } from "./implementations/SupabaseNodeMetadataRepository";
import { SupabaseTagRepository } from "./implementations/SupabaseTagRepository";

/**
 * Repository Factory
 * Provides a clean way to access repositories
 */
export class RepositoryFactory {
  private static userRepository: IUserRepository;
  private static graphRepository: IGraphRepository;
  private static nodeRepository: INodeRepository;
  private static edgeRepository: IEdgeRepository;
  private static nodeMetadataRepository: INodeMetadataRepository;
  private static tagRepository: ITagRepository;
  private static sessionId: string | undefined;
  private static isServerSide: boolean = typeof window === 'undefined';

  /**
   * Set the Clerk session ID
   */
  static setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
    // Reset repositories to create new ones with the session ID
    this.resetRepositories();
  }
  
  /**
   * Reset all repositories
   */
  private static resetRepositories(): void {
    this.userRepository = undefined as unknown as IUserRepository;
    this.graphRepository = undefined as unknown as IGraphRepository;
    this.nodeRepository = undefined as unknown as INodeRepository;
    this.edgeRepository = undefined as unknown as IEdgeRepository;
    this.nodeMetadataRepository = undefined as unknown as INodeMetadataRepository;
    this.tagRepository = undefined as unknown as ITagRepository;
  }

  /**
   * Get the user repository
   */
  static getUserRepository(): IUserRepository {
    if (!this.userRepository) {
      this.userRepository = new SupabaseUserRepository(this.sessionId);
    }
    return this.userRepository;
  }

  /**
   * Get the graph repository
   */
  static getGraphRepository(): IGraphRepository {
    if (!this.graphRepository) {
      this.graphRepository = new SupabaseGraphRepository(this.sessionId);
    }
    return this.graphRepository;
  }

  /**
   * Get the node repository
   */
  static getNodeRepository(): INodeRepository {
    if (!this.nodeRepository) {
      this.nodeRepository = new SupabaseNodeRepository(this.sessionId);
    }
    return this.nodeRepository;
  }

  /**
   * Get the edge repository
   */
  static getEdgeRepository(): IEdgeRepository {
    if (!this.edgeRepository) {
      this.edgeRepository = new SupabaseEdgeRepository();
    }
    return this.edgeRepository;
  }

  /**
   * Get the node metadata repository
   */
  static getNodeMetadataRepository(): INodeMetadataRepository {
    if (!this.nodeMetadataRepository) {
      this.nodeMetadataRepository = new SupabaseNodeMetadataRepository();
    }
    return this.nodeMetadataRepository;
  }

  /**
   * Get the tag repository
   */
  static getTagRepository(): ITagRepository {
    if (!this.tagRepository) {
      this.tagRepository = new SupabaseTagRepository(this.sessionId);
    }
    return this.tagRepository;
  }
}

// Export repositories and interfaces
export * from "./interfaces";
export * from "./implementations/SupabaseUserRepository";
export * from "./implementations/SupabaseGraphRepository";
export * from "./implementations/SupabaseNodeRepository";
export * from "./implementations/SupabaseEdgeRepository";
export * from "./implementations/SupabaseNodeMetadataRepository";
export * from "./implementations/SupabaseTagRepository";
