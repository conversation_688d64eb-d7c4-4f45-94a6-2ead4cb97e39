/**
 * Environment variables type definitions and validation
 */

// Define the expected environment variables
export interface Env {
  OPENAI_API_KEY: string;
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: string;
  CLERK_SECRET_KEY: string;
  CLERK_WEBHOOK_SIGNING_SECRET: string;
}

// Function to validate required environment variables
export function validateEnv(): Env {
  const requiredEnvVars = [
    "OPENAI_API_KEY",
    "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY",
    "CLERK_SECRET_KEY",
    "CLERK_WEBHOOK_SIGNING_SECRET",
  ];

  // Check for missing environment variables
  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar],
  );

  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(", ")}`,
    );
  }

  // Return typed environment variables
  return {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY as string,
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env
      .NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY as string,
    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY as string,
    CLERK_WEBHOOK_SIGNING_SECRET: process.env.CLERK_WEBHOOK_SIGNING_SECRET as string,
  };
}

// Export environment variables for use in the application
export const env = validateEnv();
