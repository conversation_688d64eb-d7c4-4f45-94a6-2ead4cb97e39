import React, { useMemo } from "react";
import { useGraphHistory } from "@/contexts/GraphHistoryContext";

export const useRedoButtonLogic = () => {
  const { redo, canRedo } = useGraphHistory();
  
  // Add keydown event listener for redo shortcut (Ctrl+Shift+Z or Ctrl+Y)
  const redoFn = React.useCallback((event: KeyboardEvent) => {
    if (((event.ctrlKey || event.metaKey) && event.key === 'z' && event.shiftKey) || 
        ((event.ctrlKey || event.metaKey) && event.key === 'y')) {
      event.preventDefault(); // Prevent browser's default behavior
      redo();
    }
  }, [redo]);
  
  // Set up event listener
  React.useEffect(() => {
    document.addEventListener("keydown", redoFn);
    return () => {
      document.removeEventListener("keydown", redoFn);
    };
  }, [redoFn]);

  return useMemo(() => ({
    redo,
    canRedo,
    title: "Redo"
  }), [redo, canRedo]);
};